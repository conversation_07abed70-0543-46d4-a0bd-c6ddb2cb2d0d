package com.trs.spark.util;

import com.trs.hybase.client.TRSException;
import com.trs.spark.BaseActionTest;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * *@author:wen.wen
 * *@create 2023-07-13 09:17
 **/
public class DatasetConvertUtilsTests extends BaseActionTest {

    @Test
    public void test() throws TRSException {
        // 创建一个Map，包含要生成Row的数据
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("name", "John");
        dataMap.put("age", 25);
        dataMap.put("id", null);
        dataMap.put("value", new Short[]{1, 3});
        dataMap.put("test", null);

        Map<String, Object> dataMap2 = new HashMap<>();
        dataMap2.put("name", "<PERSON>");
        dataMap2.put("age", 23);
        dataMap2.put("id", 1);
        dataMap2.put("test", null);
        dataMap2.put("value", new Short[]{1, 2});
        dataMap2.put("value2", 2);
        dataMap2.put("value3", 2);
        List<Map<String, Object>> datas = new ArrayList<>();
        datas.add(dataMap);
        datas.add(dataMap2);
        Dataset<Row> dataset = DatasetConvertUtils.javaMap2Dataset(spark, datas);
        dataset.show();
    }
}
