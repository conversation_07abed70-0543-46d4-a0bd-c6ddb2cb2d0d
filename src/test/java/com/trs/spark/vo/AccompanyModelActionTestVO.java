package com.trs.spark.vo;

import lombok.Builder;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/8/16 17:12
 * @since 1.0
 */
@Data
@Builder
public class AccompanyModelActionTestVO implements IAccompanyModelIn<AccompanyModelActionTestVO> {

    private String key;

    private Character character;

    private Integer num;

    @Override
    public int compareTo(AccompanyModelActionTestVO o) {
        return getNum().compareTo(o.getNum());
    }
}
