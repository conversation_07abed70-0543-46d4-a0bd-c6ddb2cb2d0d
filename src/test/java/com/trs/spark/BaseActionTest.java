package com.trs.spark;

import org.apache.spark.sql.SparkSession;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;

public class BaseActionTest {

    public static SparkSession spark;

    @BeforeAll
    static void init() {
        System.out.println("开始初始化SparkSession");
        if (spark == null) {
            spark = SparkSession
                    .builder()
                    .master("local[2]")
                    .enableHiveSupport()
                    .appName(BaseActionTest.class.getName())
                    .getOrCreate();
        }
    }

    @AfterAll
    static void close() {
        System.out.println("开始销毁SparkSession");
        if (spark != null) {
            spark.stop();
        }
    }
}
