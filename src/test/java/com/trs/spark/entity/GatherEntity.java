package com.trs.spark.entity;

import com.trs.common.utils.TimeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 聚合实体
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/3/21 17:57
 * @version 1.0
 * @since 1.0
 */
@Data
@NoArgsConstructor
public class GatherEntity implements Serializable {

    /**
     * 记录id
     */
    private String recordId;

    /**
     * 对象id，例如：身份证、车牌号等
     */
    private String objectId;

    /**
     * 地点，可以是地名，也可以是geohash
     */
    private String place;

    /**
     * 用来聚合的时间，因为了更快的进行聚合，会对行为时间进行处理
     */
    private String statisticTime;

    /**
     * 行为开始时间
     */
    private Timestamp activityTime;

    /**
     * 行为结束时间
     */
    private Date endTime;

    /**
     * 表名
     */
    private String tableName;

    private String key;

    private Timestamp startTime;

    /**
     * 经度
     */
    private Double wdwgs84;

    /**
     * 纬度
     */
    private Double jdwgs84;

    /**
     * 经度
     */
    private Double newWdwgs84;

    /**
     * 纬度
     */
    private Double newJdwgs84;

    private String newGeohash;

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }
        if (!(o instanceof GatherEntity)) {
            return false;
        }
        if (o == this) {
            return true;
        }
        return getObjectId().equals(((GatherEntity) o).getObjectId());
    }

    public GatherEntity(String recordId, String objectId, String place,
                        Double jdwgs84, Double wdwgs84,
                        Timestamp activityTime) {
        this.recordId = recordId;
        this.objectId = objectId;
        this.place = place;
        this.jdwgs84 = jdwgs84;
        this.wdwgs84 = wdwgs84;
        this.activityTime = activityTime;
        startTime = new Timestamp(TimeUtils.stringToDate("2023-03-22 00:00:00").getTime());
    }
}
