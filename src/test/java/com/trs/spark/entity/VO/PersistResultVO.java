package com.trs.spark.entity.VO;

import com.trs.spark.entity.DataBaseInfo;
import com.trs.spark.entity.RecordInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 持久化结果
 * *@author:wen.wen
 * *@create 2022-06-28 17:58
 **/
@Data
public class PersistResultVO implements Serializable {

    /**
     * 执行的开始时间
     */
    private long startTime;

    /**
     * 成功的记录
     */
    private List<RecordInfo> successList;

    /**
     * 失败的记录
     */
    private List<RecordInfo> failList;

    /**
     * 失败的错误信息
     */
    private String errorMsg;

    /**
     * 插入失败数据的数据库信息
     */
    private DataBaseInfo dataBaseInfo;
}
