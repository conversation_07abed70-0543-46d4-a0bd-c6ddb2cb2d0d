package com.trs.spark.entity;

import java.io.Serializable;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/22 16:08
 * @version: 1.0
 */
public class Person implements Serializable {

    private String idCard;

    private String name;

    private Integer age;

    public Person() {
    }

    public Person(String idCard, String name, Integer age) {
        this.idCard = idCard;
        this.name = name;
        this.age = age;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Override
    public String toString() {
        return "Person{" +
                "idCard='" + idCard + '\'' +
                ", name='" + name + '\'' +
                ", age=" + age +
                '}';
    }
}
