package com.trs.spark.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description 单条记录信息
 * <AUTHOR>
 * @Date 2021/4/13
 * @Version 1.0
 * @ClassName com.trs.db.sdk.pojo.RecordInfo
 */
@Data
public class RecordInfo implements Serializable {

    private static final long serialVersionUID = 3634808642894497353L;

    /**
     * 唯一id
     */
    private String uid;

    /**
     * 唯一ID字段的名字
     */
    private String uidName;

    /**
     * 业务相关的主键ID（插入时如果uid为空就填充该值）
     */
    private String tableId;

    /**
     * 业务相关的主键ID字段名
     */
    private String tableIdName;

    /**
     * 表名称
     */
    private String indexName;

    /**
     * 检索结果的得分/相关性
     */
    private double relevance;

    /**
     * 其他字段信息
     */
    private Map<String, Object> fieldValueMap = new HashMap<>();

    /**
     * 其他的一些额外属性
     */
    private Map<String, Object> extraInfo = new HashMap<>();


    /**
     * 设置字段值
     *
     * @param fieldName
     * @param value
     */
    public void setFieldValue(String fieldName, Object value) {
        fieldValueMap.put(fieldName, value);
    }

    /**
     * 获取字段值
     *
     * @param fieldName
     * @return
     */
    public Object getFieldValue(String fieldName) {
        return fieldValueMap.get(fieldName);
    }

    /**
     * 设置额外属性
     *
     * @param key   key
     * @param value value
     */
    public void setExtraInfoValue(String key, Object value) {
        extraInfo.put(key, value);
    }

    /**
     * 获取额外属性值
     *
     * @param key 名称
     */
    public Object getExtraInfoValue(String key) {
        return extraInfo.get(key);
    }
}
