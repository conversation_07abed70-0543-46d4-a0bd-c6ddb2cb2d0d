package com.trs.spark.entity;

import com.trs.common.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StayEntity implements Serializable {

    /**
     * 对象id，例如：身份证、车牌号等
     */
    private String objectId;

    /**
     * 活动地点
     */
    private String geohash;

    /**
     * 活动时间
     */
    private Long hdsj;

    private String time;

    /**
     * 经度
     */
    private String jdwgs84;

    /**
     * 纬度
     */
    private String wdwgs84;

    private String nextPlace;

    private Double stayTime;

    private Double quantile_stay_time;

    private Double middleStayTime;

    public StayEntity(String objectId, String geohash, Long hdsj) {
        this.objectId = objectId;
        this.geohash = geohash;
        this.hdsj = hdsj;
    }

    public void setHdsj(Long hdsj) {
        this.hdsj = hdsj;
        this.time = TimeUtils.dateToString(new Date(hdsj), TimeUtils.YYYYMMDD_HHMMSS);
    }
}
