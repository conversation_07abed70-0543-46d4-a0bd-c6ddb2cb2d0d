package com.trs.spark.entity;

import lombok.Data;

/**
 * 需要进行持久化的消息结构
 * *@author:wen.wen
 * *@create 2021-10-14 15:45
 **/
@Data
public class PersistMessageDTO {

    /**
     * 数据库信息
     */
    private DataBaseInfo dataBaseInfo;

    /**
     * 需要进行持久化的记录
     */
    private RecordInfo recordInfo;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 创建时间
     */
    private String createTime;
}
