package com.trs.spark.entity;

import java.io.Serializable;
import java.sql.Timestamp;

public class ChannelAnd<PERSON><PERSON>wordLib implements Serializable {

    private Long id;

    private String cr_user;


    private Timestamp cr_time;

    private Long channel_id;
    private Long keyword_lib_id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCr_user() {
        return cr_user;
    }

    public void setCr_user(String cr_user) {
        this.cr_user = cr_user;
    }

    public Timestamp getCr_time() {
        return cr_time;
    }

    public void setCr_time(Timestamp cr_time) {
        this.cr_time = cr_time;
    }

    public Long getChannel_id() {
        return channel_id;
    }

    public void setChannel_id(Long channel_id) {
        this.channel_id = channel_id;
    }

    public Long getKeyword_lib_id() {
        return keyword_lib_id;
    }

    public void setKeyword_lib_id(Long keyword_lib_id) {
        this.keyword_lib_id = keyword_lib_id;
    }

    @Override
    public String toString() {
        return "ChannelAndKeywordLib{" +
                "id=" + id +
                ", cr_user='" + cr_user + '\'' +
                ", cr_time=" + cr_time +
                ", channel_id=" + channel_id +
                ", keyword_lib_id=" + keyword_lib_id +
                '}';
    }
}
