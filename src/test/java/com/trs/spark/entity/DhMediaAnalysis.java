package com.trs.spark.entity;

import java.io.Serializable;

/**
 * *@author:wen.wen
 * *@create 2023-03-20 17:06
 **/
public class DhMediaAnalysis implements Serializable {

    private String IR_URLTITLE;

    private String IR_SID;

    private String IR_URLTIME;

    public String getIR_URLTITLE() {
        return IR_URLTITLE;
    }

    public void setIR_URLTITLE(String IR_URLTITLE) {
        this.IR_URLTITLE = IR_URLTITLE;
    }

    public String getIR_SID() {
        return IR_SID;
    }

    public void setIR_SID(String IR_SID) {
        this.IR_SID = IR_SID;
    }

    public String getIR_URLTIME() {
        return IR_URLTIME;
    }

    public void setIR_URLTIME(String IR_URLTIME) {
        this.IR_URLTIME = IR_URLTIME;
    }
}
