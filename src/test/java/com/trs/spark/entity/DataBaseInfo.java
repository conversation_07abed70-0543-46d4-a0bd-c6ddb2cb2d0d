package com.trs.spark.entity;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 数据源信息
 * *@author:wen.wen
 * *@create 2021-03-30 20:39
 **/
@Data
@ToString
public class DataBaseInfo implements Serializable {

    private static final long serialVersionUID = -3665804199014368530L;

    private Integer dataSourceId;

    /**
     * Hybase.海贝
     * ES
     * Mysql
     */
    private String dbType;

    private String dbName;

    private String tableName;


    private Integer tableId;

    private String host;

    private String port;

    private String userName;

    private String password;
}
