package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.BasePeekAction;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.action.impl.peek.PrintRddItemAction;
import com.trs.spark.process.SparkProcessBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;
import org.junit.jupiter.api.Test;
import scala.Tuple2;

import java.util.Arrays;

@Slf4j
class JavaPairRddToJavaRddActionTest extends BaseActionTest {

    public static class Demo extends BasePeekAction<JavaRDD<String>> {

        @Override
        public void doPeek(SparkSession spark, JavaRDD<String> data) {
            for (Tuple2<String, String> tuple2 : spark.sparkContext().getConf().getAll()) {
                System.out.println(tuple2._1 + "=" + tuple2._2);
            }
        }
    }

    @Test
    void doAction() {
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("1", "2", "3")))
                .peek(new Demo())
                .convert(new JavaRddToJavaPairRddStringKeyConvertAction<String, Integer>(i -> i, i -> Integer.valueOf(i)))
                .convert(new JavaPairRddToJavaRddAction<String, Integer>())
                .peek(new PrintRddItemAction<Integer>(rdd -> "rdd的item数据为：" + rdd, item -> System.out.println(item)))
                .doRun(spark);
    }
}