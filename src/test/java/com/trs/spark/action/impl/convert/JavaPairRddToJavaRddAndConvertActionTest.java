package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.action.impl.peek.PrintRddItemAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

class JavaPairRddToJavaRddAndConvertActionTest extends BaseActionTest {

    @Test
    void doAction() {
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("1", "2", "3")))
                .convert(new JavaRddToJavaPairRddStringKeyConvertAction<String, Integer>(i -> i, i -> Integer.valueOf(i)))
                .convert(new JavaPairRddToJavaRddAndConvertAction<String, Integer, String>(rdd -> rdd._1))
                .convert(new JavaRddToJavaPairRddConvertAction<String, String, String>(i -> i, i -> i))
                .convert(new JavaPairRddToJavaRddAndConvertAction<String, String, String>(rdd -> rdd._2))
                .peek(new PrintRddItemAction<String>(rdd -> "rdd的item数据为：" + rdd, item -> System.out.println(item)))
                .doRun(spark);
    }
}