package com.trs.spark.action.impl.convert;

import com.trs.common.utils.TimeUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.entity.GatherEntity;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.junit.jupiter.api.Test;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

public class GatherActionTest extends BaseActionTest implements Serializable {


    public List<GatherEntity> getData() {
        List<GatherEntity> list = new ArrayList<>();
        list.add(new GatherEntity("01", "1","a区", 104.21414,  30.87111, new Timestamp(TimeUtils.stringToDate("2023-03-22 08:31:30").getTime())));
        list.add(new GatherEntity("02", "1","a区", 104.21414,  30.87111, new Timestamp(TimeUtils.stringToDate("2023-03-22 08:31:30").getTime())));
        list.add(new GatherEntity("03", "2","b区", 104.21424,  30.87211, new Timestamp(TimeUtils.stringToDate("2023-03-22 08:12:30").getTime())));
        list.add(new GatherEntity("04", "2","b区", 104.21424,  30.87211, new Timestamp(TimeUtils.stringToDate("2023-03-22 08:13:30").getTime())));
        list.add(new GatherEntity("03", "3","a区", 104.21434,  30.87311, new Timestamp(TimeUtils.stringToDate("2023-03-22 08:33:30").getTime())));
        list.add(new GatherEntity("04", "4","a区", 104.21444,  30.87411, new Timestamp(TimeUtils.stringToDate("2023-03-22 08:34:30").getTime())));
        list.add(new GatherEntity("11", "1","b区", 104.21454,  30.87511, new Timestamp(TimeUtils.stringToDate("2023-03-22 09:10:30").getTime())));
        list.add(new GatherEntity("12", "1","b区", 104.21464,  30.87611, new Timestamp(TimeUtils.stringToDate("2023-03-22 09:31:30").getTime())));
        list.add(new GatherEntity("13", "2","b区", 104.21474,  30.87711, new Timestamp(TimeUtils.stringToDate("2023-03-22 09:13:30").getTime())));
        list.add(new GatherEntity("14", "3","b区", 104.21484,  30.87811, new Timestamp(TimeUtils.stringToDate("2023-03-22 09:43:30").getTime())));
        return list;
    }

    @Test
    void doAction() {
        JavaSparkContext jsc = new JavaSparkContext(spark.sparkContext());
        JavaRDD<GatherEntity> javaRDD = jsc.parallelize(getData());
        GatherAction2 gatherAction = new GatherAction2(2, 30, "objectId",  "place", "startTime", "activityTime",  GatherEntity.class);
        JavaRDD javaRDD1 = gatherAction.doAction(spark, javaRDD);
        javaRDD1.collect().forEach(System.out::println);
    }

    @Test
    void doMultipleAction() {
        JavaSparkContext jsc = new JavaSparkContext(spark.sparkContext());
        JavaRDD<GatherEntity> javaRDD = jsc.parallelize(getData());
        MultipleGatherAction action = new MultipleGatherAction()
                .threshold(2)
                .intervalTime(30)
                .objectId("objectId")
                .startTime("startTime")
                .time("activityTime")
                .targetClass(GatherEntity.class)
                .distanceThreshold(500)
                .jdwgs84("jdwgs84")
                .wdwgs84("wdwgs84");
        JavaRDD javaRDD1 = action.doAction(spark, javaRDD);
        javaRDD1.collect().forEach(System.out::println);
    }

    private ArrayList<String> convertToStandardTime(Date date, Integer intervalTime, String place) {
        ArrayList<String> standardTimes = new ArrayList<>(Arrays.asList(TimeUtils.dateToString(date, TimeUtils.YYYYMMDD_HH1)));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (calendar.get(Calendar.MINUTE) - intervalTime < 0) {
            standardTimes.add(TimeUtils.hourDefOrAft(TimeUtils.dateToString(date), -1, TimeUtils.DEFAULT_YYYYMMDD_HHMMSS, TimeUtils.YYYYMMDD_HH1));
        }
        return (ArrayList) standardTimes.stream().map(a -> String.format("%s_%s", a, place)).collect(Collectors.toList());
    }

    private ArrayList<GatherEntity> distinctData(ArrayList<GatherEntity> pv1) {
        return pv1.stream().collect(Collectors.
                collectingAndThen(Collectors.
                        toCollection(() -> new TreeSet<>(Comparator.
                                comparing(GatherEntity::getObjectId))), ArrayList::new));
    }
}
