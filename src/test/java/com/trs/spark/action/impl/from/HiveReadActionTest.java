package com.trs.spark.action.impl.from;

import com.trs.spark.BaseActionTest;
import com.trs.spark.datasource.HiveSource;
import com.trs.spark.entity.Person;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

public class HiveReadActionTest extends BaseActionTest {

    @Test
    public void testRead(){
        HiveReadAction<Person> action = new HiveReadAction<>(
                "cd_dev_iceberg.iceberg_dev_hadoop.person",
                Person.class
        );
        JavaRDD<Person> out = SparkProcessBuilder.newProcess()
                .from(action)
                .doRun(spark);
        out.foreach(r -> System.out.println(r));
    }

    @Test
    public void testWrite(){
        HiveSource hiveSource = new HiveSource();
        Person person = new Person("1", "xx", 22);
        Dataset<Row> dataset = spark.createDataFrame(Arrays.asList(person), Person.class);
        hiveSource.writeDataToSource(spark, null,"person", dataset, SaveMode.Overwrite);
        Dataset<Row> dataset1 = hiveSource.loadDataFromSource(spark, null, "person");
        dataset1.show();
    }
}