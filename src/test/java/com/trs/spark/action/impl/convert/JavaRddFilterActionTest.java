package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.action.impl.peek.PrintRddItemAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

class JavaRddFilterActionTest extends BaseActionTest {

    @Test
    void doAction() {
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("1", "2", "3")))
                .convert(new JavaRddConvertEntityAction<String, Integer>(i -> Integer.valueOf(i)))
                .filter(new JavaRddFilterAction<Integer>(i -> i >= 3))
                .peek(new PrintRddItemAction<Integer>(rdd -> "过滤后rdd的item数据为：" + rdd, item -> System.out.println(item)))
                .doRun(spark);
    }
}