package com.trs.spark.action.impl.from;

import com.trs.spark.BaseActionTest;
import com.trs.spark.entity.ChannelAndKeywordLib;
import com.trs.spark.function.Function;
import com.trs.spark.process.SparkProcessBuilder;
import lombok.var;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.api.java.UDF2;
import org.apache.spark.sql.types.DataTypes;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BaseSourceReadActionTest extends BaseActionTest {

    private String url = "*************************************";

    private String table = "channel_and_keyword_lib";

    private String user = "root";

    private String password = "!QAZ2wsx1234";

    @Test
    public void doAction() {
        Function<Row, ChannelAndKeywordLib> function2 = row -> {
            ChannelAndKeywordLib lib = new ChannelAndKeywordLib();
            lib.setChannel_id(row.getLong(0));
            lib.setId(row.getLong(1));
            lib.setKeyword_lib_id(row.getLong(2));
            lib.setCr_time(row.getTimestamp(3));
            lib.setCr_user(row.getString(4));
            Object cc = row.getAs("cc");
            System.out.println("cc=" + cc);
            return lib;
        };
        var base = new BaseSourceReadAction<>(url, table, user, password, "id<1000", null, function2);
        base.addCustomDatasetRow(rows -> {
            String[] columns = "channel_id,id,keyword_lib_id,cr_time,cr_user".split(",");
            List<String> s = new ArrayList<>(Arrays.asList(columns));
            s.add("addS1AndS2(channel_id,id) AS cc");
            s.add("modLong(id) AS mId");
            return rows.selectExpr(s.toArray(new String[0])).where("mId=1");
        });
        spark.udf().register(
                "addS1AndS2", (UDF2<Long, Long, String>) (s1, s2) -> s1 + "--" + s2,
                DataTypes.StringType
        );
        spark.udf().register(
                "modLong", (UDF1<Long, Long>) (s1) -> s1 % 2,
                DataTypes.LongType
        );
        JavaRDD<ChannelAndKeywordLib> out = SparkProcessBuilder.newProcess()
                .from(base)
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByFunction这个是id<1000的查询结果:" + item));

    }
}