package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.FileReadAction;
import com.trs.spark.entity.Point;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaPairRDD;
import org.junit.jupiter.api.Test;
import scala.Tuple2;

import java.util.ArrayList;

class DBSCANActionTest extends BaseActionTest {

    @Test
    void doAction() {
        JavaPairRDD<Point, ArrayList<Point>> rdd =
                SparkProcessBuilder.newProcess().from(
                        new FileReadAction<>(
                                "src/test/resources/Point.txt",
                                str -> {
                                    String[] pointXY = str.split(" ", 2);
                                    return new Point(Double.valueOf(pointXY[0]), Double.valueOf(pointXY[1]));
                                }
                        )
                ).convert(new DBSCANAction<>(2.0, 3L, Point::calculateMHDDistance)).doRun(spark);
        for (Tuple2<Point, ArrayList<Point>> tuple2 : rdd.collect()) {
            System.out.println("核心点为：" + tuple2._1);
            System.out.println("样本点为：" + tuple2._2);
            System.out.println("个数为：" + tuple2._2.size());
            System.out.println("==================");
        }
    }
}