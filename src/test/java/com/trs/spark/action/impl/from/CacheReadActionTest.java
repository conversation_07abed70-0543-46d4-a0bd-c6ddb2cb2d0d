package com.trs.spark.action.impl.from;

import com.trs.common.datasource.PreLoads;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.CacheActionSourceFactory;
import com.trs.spark.cache.CacheAction;
import com.trs.spark.cache.coder.JsonArrayCoder;
import com.trs.spark.configuration.Configurations;
import com.trs.spark.configuration.WhereIsConfig;
import com.trs.spark.constant.RunMode;
import com.trs.spark.datasource.HDFSSource;
import com.trs.common.datasource.parser.IcebergSourceUrlParser;
import com.trs.spark.entity.Person;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.trs.spark.constant.CacheConstant.CACHE_HDFS_PREFIX;
import static com.trs.spark.constant.CacheConstant.CACHE_URL;

public class CacheReadActionTest extends BaseActionTest {

    @Test
    public void cacheInfoFromConfTest() {
        // 初始化配置
        Map<String, Object> map = new HashMap<>();
        map.put("spark.master", "test");
        map.put(CACHE_HDFS_PREFIX, "/user/trs");
        map.put(CACHE_URL, "hdfs://10.18.20.101:8020");
        WhereIsConfig<Map<String, Object>> mapConfig = Configurations.inMap(map);
        Configurations.getLoader(mapConfig).loadIntoSystem(RunMode.DEVELOPMENT);

        // 加载数据的action
        BaseSourceReadAction<Person> readAction = getReadAction();

        // 缓存的action
        CacheReadAction<Person> cacheReadAction = CacheReadActionBuilder
                // 必要参数 （readAction） 公因条件非必须
                .newBuilder(readAction, "age >= 18")
                // 编解码器-非必须 默认jsonArray
                .cacheCoder(new JsonArrayCoder())
                // 字段类型转换
                .addFieldType("age", "int")
                .build();

        // 加载数据
        JavaRDD<Person> out = SparkProcessBuilder.newProcess()
                .from(cacheReadAction)
                .doRun(spark);
        out.foreach(r -> System.out.println(r));
    }

    /**
     * 模拟大数据量写入hdfs
     * ps: 本来想写2000w条数据的 内存抗不住 -_o!
     */
    @Test
    public void mockCacheData() {
        BaseSourceReadAction<Person> action = new BaseSourceReadAction<>(
                "iceberg:thrift://master.hadoop.test:9083?catalogName=cd_dev_iceberg",
                "cd_dev_iceberg.iceberg_dev.person1",
                "",
                "",
                "age >= 0",
                Person.class
        );
        JavaRDD<Person> out = SparkProcessBuilder.newProcess()
                .from(action)
                .doRun(spark);
        // 复制数据并转换为Dataset
        JavaRDD<Person> replicatedRDD = out.flatMap(person -> {
            List<Person> replicatedPersons = new ArrayList<>();
            for (int i = 0; i < 1_000_000; i++) {
                replicatedPersons.add(person);
            }
            return replicatedPersons.iterator();
        });
        // 将复制后的RDD转换为Dataset
        Dataset<Row> replicatedDataset = spark.createDataFrame(replicatedRDD, Person.class);

        SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver("hdfs://10.18.20.101:8020", "root", "root").get();
        CacheAction cacheAction = CacheActionSourceFactory.actionFromDriver(sourceDriver);
        Dataset dataset = new JsonArrayCoder().enCode(spark, replicatedDataset);
        cacheAction.doCache(spark, dataset, action.getTable(), action.searchParams(), "");
    }

    /**
     * 读取700w条数据测试
     */
    @Test
    public void readBigDataTest() {
        BaseSourceReadAction<Person> action = new BaseSourceReadAction<>(
                "iceberg:thrift://master.hadoop.test:9083?catalogName=cd_dev_iceberg",
                "cd_dev_iceberg.iceberg_dev.person1",
                "",
                "",
                "age >= 0",
                Person.class
        );
        // 缓存的action装饰加载数据的action
        SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver("hdfs://10.18.20.101:8020", "root", "root").get();
        CacheReadAction<Person> cacheReadAction = new CacheReadAction<>(action);

        // 加载数据
        JavaRDD<Person> out = SparkProcessBuilder.newProcess()
                .from(cacheReadAction)
                .doRun(spark);

    }


    private BaseSourceReadAction<Person> getReadAction() {
        BaseSourceReadAction<Person> action = new BaseSourceReadAction<>(
                "iceberg:thrift://master.hadoop.test:9083?catalogName=cd_dev_iceberg",
                "cd_dev_iceberg.iceberg_dev.person1",
                "",
                "",
                "age > 18",
                Person.class
        );
        return action;
    }

    static {
        // 初始化一些配置 防止空指针
        HDFSSource source = new HDFSSource();
        PreLoads.initVirtualSourceUrlLoadings(new IcebergSourceUrlParser());
        Map<String, Object> map = new HashMap<>();
        map.put("spark.master", "test");
        map.put(CACHE_HDFS_PREFIX, "/user/trs");
        WhereIsConfig<Map<String, Object>> mapConfig = Configurations.inMap(map);
        Configurations.getLoader(mapConfig).loadIntoSystem(RunMode.DEVELOPMENT);
    }
}
