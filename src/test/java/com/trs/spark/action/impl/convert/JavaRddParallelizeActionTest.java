package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.action.impl.peek.PrintRddItemAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;

class JavaRddParallelizeActionTest extends BaseActionTest {

    @Test
    void doAction() {
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("1", "2", "3")))
                .convert(new JavaRddConvertEntityAction<String, Integer>(i -> Integer.valueOf(i)))
                .convert(new CommonConvertAction<JavaRDD<Integer>, Integer>(rdd -> rdd.fold(0, (a, b) -> a + b)))
                .convert(new CommonConvertAction<Integer, ArrayList<String>>(i -> new ArrayList<>(Arrays.asList("数据和为" + i))))
                .convert(new JavaRddParallelizeAction<String>())
                .peek(new PrintRddItemAction<String>(rdd -> "rdd的item数据为：" + rdd, item -> System.out.println(item)))
                .doRun(spark);
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("5", "10", "15", "20")))
                .convert(new JavaRddConvertEntityAction<String, Integer>(i -> Integer.valueOf(i)))
                .convert(new CommonConvertAction<JavaRDD<Integer>, Integer>(rdd -> rdd.fold(0, (a, b) -> a + b)))
                .convert(new CommonConvertAction<Integer, ArrayList<String>>(i -> new ArrayList<>(Arrays.asList("2:数据和为" + i))))
                .convert(new JavaRddParallelizeAction<String>(3))
                .peek(new PrintRddItemAction<String>(rdd -> "2:rdd的item数据为：" + rdd, item -> System.out.println(item)))
                .doRun(spark);
    }
}