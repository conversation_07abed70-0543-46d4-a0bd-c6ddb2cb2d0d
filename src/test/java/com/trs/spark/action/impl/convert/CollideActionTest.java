package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.JDBCReadAction;
import com.trs.spark.action.impl.from.MultipleDataSourcesWithConvertValuesMapReadAction;
import com.trs.spark.action.impl.peek.PrintRddCountAction;
import com.trs.spark.entity.ChannelAndKeywordLib;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;
import scala.Tuple2;

import java.util.ArrayList;
import java.util.HashMap;

class CollideActionTest extends BaseActionTest {

    private String url = "*******************************************************************************************************************************************************************";
    private String table = "channel_and_keyword_lib";

    private String user = "root";
    private String password = "!QAZ2wsx1234";

    @Test
    void doAction() {
        SparkProcessBuilder<JavaRDD<ChannelAndKeywordLib>> one = SparkProcessBuilder.newProcess()
                .from(new JDBCReadAction<>(url, table, user, password, ChannelAndKeywordLib.class))
                .peek(new PrintRddCountAction<>(count -> "数量为" + count));
        SparkProcessBuilder<JavaRDD<ChannelAndKeywordLib>> two = SparkProcessBuilder.newProcess()
                .from(new JDBCReadAction<>(url, table, user, password, "id<833", ChannelAndKeywordLib.class))
                .peek(new PrintRddCountAction<>(count -> "数量为" + count));

        HashMap<String, SparkProcessBuilder<JavaRDD<ChannelAndKeywordLib>>> builders = new HashMap<>();
        builders.put("one", one);
        builders.put("two", two);

        JavaPairRDD<String, ArrayList<ChannelAndKeywordLib>> r = SparkProcessBuilder.newProcess().from(
                new MultipleDataSourcesWithConvertValuesMapReadAction<>(
                        builders,
                        item ->
                                item.mapPartitionsToPair(in -> {
                                    ArrayList<Tuple2<String, ChannelAndKeywordLib>> data = new ArrayList<>();
                                    while (in.hasNext()) {
                                        ChannelAndKeywordLib channelAndKeywordLib = in.next();
                                        data.add(new Tuple2<>(channelAndKeywordLib.getId().toString(), channelAndKeywordLib));
                                    }
                                    return data.iterator();
                                })
                )
        ).convert(new CollideAction<ChannelAndKeywordLib>(2, "one + two")).doRun(spark);
        r.foreach(item -> System.out.println(item._1));
    }
}