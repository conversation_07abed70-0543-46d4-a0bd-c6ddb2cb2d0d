package com.trs.spark.action.impl.from;

import com.alibaba.fastjson.JSONObject;
import com.trs.spark.BaseActionTest;
import com.trs.spark.Teacher;
import com.trs.spark.function.Function;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Row;
import org.junit.jupiter.api.Test;

class HDFSReadActionTest extends BaseActionTest {

    private String url = "hdfs://127.0.0.1:9000";
    private String file = "/teacher.json";

    private String user = "";
    private String password = "";


    @Test
    void doActionConvertByFunction() {
        Function<Row, Teacher> function = row -> {
            Teacher teacher = new Teacher();
            teacher.setId(Integer.valueOf(row.getAs("id")));
            teacher.setName(row.getAs("name"));
            teacher.setAge(Integer.valueOf(row.getAs("age")));
            teacher.setHigh(Double.valueOf(row.getAs("high")));
            return teacher;
        };

        JavaRDD<Teacher> out1 = SparkProcessBuilder.newProcess()
                .from(new HDFSReadAction<>(url, file, user, password, "", function))
                .doRun(spark);
        out1.collect().forEach(item -> System.out.println("doActionConvertByFunction这个是查询结果:" + JSONObject.toJSONString(item)));

        JavaRDD<Teacher> out2 = SparkProcessBuilder.newProcess()
                .from(new HDFSReadAction<>(url, file, user, password, "age > 10 and high > 50", function))
                .doRun(spark);
        out2.collect().forEach(item -> System.out.println("doActionConvertByFunction这个是age > 10 and high > 170查询结果:" + JSONObject.toJSONString(item)));
    }

    @Test
    void doActionConvertByAS() {
        JavaRDD<Teacher> out = SparkProcessBuilder.newProcess()
                .from(new HDFSReadAction<>(url, file, user, password, "",Teacher.class))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByAS这个是查询结果:" + JSONObject.toJSONString(item)));

        out = SparkProcessBuilder.newProcess()
                .from(new HDFSReadAction<>(url, file, user, password, "age > 10 and high > 50", Teacher.class))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByAS这个是age > 10 and high > 170的查询结果:" + JSONObject.toJSONString(item)));
    }
}