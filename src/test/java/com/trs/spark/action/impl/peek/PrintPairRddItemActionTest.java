package com.trs.spark.action.impl.peek;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.convert.JavaRddToJavaPairRddStringKeyConvertAction;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaPairRDD;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

class PrintPairRddItemActionTest extends BaseActionTest {

    @Test
    void doAction() {
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("1", "2", "3")))
                .convert(new JavaRddToJavaPairRddStringKeyConvertAction<String, Integer>(i -> i, i -> Integer.valueOf(i)))
                .peek(new PrintPairRddCountAction<String, Integer>(rdd -> "PrintPairRddCountAction：" + rdd, item -> System.out.println(item)))
                .peek(new PrintPairRddCountAction<String, Integer>(rdd -> "不应该打印出来，PrintPairRddCountAction：" + rdd, item -> System.out.println(item), false))
                .peek(new PrintPairRddAction<String, Integer>(rdd -> "PrintPairRddAction：" + rdd, item -> System.out.println(item)))
                .peek(new PrintPairRddAction<String, Integer>(rdd -> "不应该打印出来，PrintPairRddAction：" + rdd, item -> System.out.println(item), false))
                .peek(new PrintPairRddItemAction<String, Integer>(rdd -> "PrintPairRddItemAction：" + rdd, item -> System.out.println(item)))
                .peek(new PrintPairRddItemAction<String, Integer>(rdd -> "不应该打印出来，PrintPairRddItemAction：" + rdd, item -> System.out.println(item), false))
                .doRun(spark);
    }
}