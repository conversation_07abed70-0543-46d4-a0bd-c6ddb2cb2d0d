package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.action.impl.peek.PrintPairRddItemAction;
import com.trs.spark.dto.AccompanyModelDTO;
import com.trs.spark.process.SparkProcessBuilder;
import com.trs.spark.vo.AccompanyModelActionTestVO;
import org.apache.spark.api.java.JavaPairRDD;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

class AccompanyModelActionTest extends BaseActionTest {

    @Test
    void doAction() {
        ArrayList<AccompanyModelActionTestVO> all = new ArrayList<>(0);
        int i = 0;
        for (char c : "ARWQRQBCBDASFJIOAAB".toCharArray()) {
            all.add(
                    AccompanyModelActionTestVO.builder()
                            .key("one")
                            .num(i++)
                            .character(c)
                            .build()
            );
        }
        for (char c : "BDREQWTRCWABQRQWRQWRTYKOEQPA".toCharArray()) {
            all.add(
                    AccompanyModelActionTestVO.builder()
                            .key("two")
                            .num(i++)
                            .character(c)
                            .build()
            );
        }
        for (char c : "BCDREQWTRCWASDBQRQWRSAFQWRGTRHYKOEQPA".toCharArray()) {
            all.add(
                    AccompanyModelActionTestVO.builder()
                            .key("third")
                            .num(i++)
                            .character(c)
                            .build()
            );
        }
        AccompanyModelDTO<String, AccompanyModelActionTestVO> dto = new AccompanyModelDTO<>(
                3,
                0.4,
                AccompanyModelActionTestVO::getKey,
                Arrays.asList("one", "two", "third"),
                Collections.singletonList((a, b) -> a.getCharacter().equals(b.getCharacter()))
        );
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(all))
                .convert(new AccompanyModelAction<>(dto))
                .peek(new PrintPairRddItemAction<>(rdd -> "过滤后rdd的item数据为：" + rdd, item -> System.out.println(item)))
                .doRun(spark);
    }
}