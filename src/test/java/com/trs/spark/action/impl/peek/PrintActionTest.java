package com.trs.spark.action.impl.peek;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.convert.JavaRddConvertEntityAction;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

class PrintActionTest extends BaseActionTest {

    @Test
    void doAction() {
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("1", "2", "3")))
                .convert(new JavaRddConvertEntityAction<String, Integer>(i -> Integer.valueOf(i)))
                .peek(new PrintAction<JavaRDD<Integer>>(rdd -> "rdd数据为：" + rdd.collect(), item -> System.out.println(item)))
                .peek(new PrintAction<JavaRDD<Integer>>(rdd -> "这个不应该打印出来，rdd数据为：" + rdd.collect(), item -> System.out.println(item), false))
                .doRun(spark);
    }
}