package com.trs.spark.action.impl.from;

import com.trs.spark.BaseActionTest;
import com.trs.spark.entity.ChannelAndKeywordLib;
import com.trs.spark.function.Function;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Row;
import org.junit.jupiter.api.Test;

class JDBCReadActionTest extends BaseActionTest {

    private String url = "*******************************************************************************************************************************************************************";
    private String table = "channel_and_keyword_lib";

    private String user = "root";
    private String password = "!QAZ2wsx1234";


    @Test
    void doActionConvertByFunction() {
        Function<Row, ChannelAndKeywordLib> function = row -> {
            ChannelAndKeywordLib lib = new ChannelAndKeywordLib();
            lib.setChannel_id(row.getAs("channel_id"));
            lib.setId(row.getAs("id"));
            lib.setCr_time(row.getAs("cr_time"));
            lib.setCr_user(row.getAs("cr_user"));
            lib.setKeyword_lib_id(row.getAs("keyword_lib_id"));
            return lib;
        };
        JavaRDD<ChannelAndKeywordLib> out = SparkProcessBuilder.newProcess()
                .from(new JDBCReadAction<>(url, table, user, password, function))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByFunction这个是查询结果:" + item));


        Function<Row, ChannelAndKeywordLib> function2 = row -> {
            ChannelAndKeywordLib lib = new ChannelAndKeywordLib();
            lib.setChannel_id(row.getLong(0));
            lib.setId(row.getLong(1));
            lib.setKeyword_lib_id(row.getLong(2));
            lib.setCr_time(row.getTimestamp(3));
            lib.setCr_user(row.getString(4));
            return lib;
        };
        out = SparkProcessBuilder.newProcess()
                .from(new JDBCReadAction<>(url, table, user, password, "id<1000", "channel_id,id,keyword_lib_id,cr_time,cr_user,cr_user as cc", function2))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByFunction这个是id<1000的查询结果:" + item));
    }

    @Test
    void doActionConvertByAS() {
        JavaRDD<ChannelAndKeywordLib> out = SparkProcessBuilder.newProcess()
                .from(new JDBCReadAction<>(url, table, user, password, ChannelAndKeywordLib.class))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByAS这个是查询结果:" + item));

        out = SparkProcessBuilder.newProcess()
                .from(new JDBCReadAction<>(url, table, user, password, "id<649", ChannelAndKeywordLib.class))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByAS这个是id<649的查询结果:" + item));
    }
}