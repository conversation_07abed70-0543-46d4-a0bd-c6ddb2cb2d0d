package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.action.impl.peek.PrintRddItemAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

class JavaRddFilterAndConvertActionTest extends BaseActionTest {

    @Test
    void doAction() {
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("1", "2", "3")))
                .convert(new JavaRddConvertEntityAction<String, Integer>(i -> Integer.valueOf(i)))
                .filter(new JavaRddFilterAndConvertAction<Integer, String>(i -> i >= 2, i -> "保留了" + i))
                .peek(new PrintRddItemAction<String>(rdd -> "过滤后rdd的item数据为：" + rdd, item -> System.out.println(item)))
                .doRun(spark);
    }
}