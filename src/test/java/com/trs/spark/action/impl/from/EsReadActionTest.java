package com.trs.spark.action.impl.from;

import com.trs.spark.BaseActionTest;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;


public class EsReadActionTest extends BaseActionTest {
    private String url = "es://10.18.20.131:9200;10.18.20.131:9200?es.net.ssl=false";
    private String table = "dwd_hecheng_results";

    private String user = "admin";
    private String password = "trsadmin";

    @Test
    void doActionConvertByAS() {
        JavaRDD<String> out = SparkProcessBuilder.newProcess()
                .from(new EsReadAction<>(
                        url,
                        table,
                        user,
                        password,
                        "update_time<=\"2024-06-01 00:00:00\"",
                        "update_time,id",
                        row -> String.format(
                                "%s:%s",
                                row.getLong(1),
                                row.getString(0)
                        )
                )).doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByAS这个是查询结果:" + item));

    }
}