package com.trs.spark.action.impl.from;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.convert.JavaRddConvertEntityAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

class JavaRddParallelizeFromActionTest extends BaseActionTest {

    @Test
    void doAction() {
        JavaRDD<Integer> out = SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("1", "2", "3")))
                .convert(new JavaRddConvertEntityAction<String, Integer>(i -> Integer.valueOf(i)))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("1:这个是字符串:" + item));

        JavaRDD<Integer> out2 = SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("5", "4", "3"), 3))
                .convert(new JavaRddConvertEntityAction<String, Integer>(i -> Integer.valueOf(i)))
                .doRun(spark);
        out2.collect().forEach(item -> System.out.println("2:这个是字符串:" + item));
    }
}