package com.trs.spark.action.impl.from;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.entity.DhMediaAnalysis;
import com.trs.spark.function.Function;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Row;
import org.junit.jupiter.api.Test;

import java.util.Date;

class HybaseReadActionTest extends BaseActionTest {

    private String url = "hybase://192.168.210.57:5555";
    private String table = "originalData.dh_media_analysis3";

    private String user = "admin";
    private String password = "trsadmin";


    @Test
    void doActionConvertByFunction() {
        Function<Row, DhMediaAnalysis> function = row -> {
            DhMediaAnalysis analysis = new DhMediaAnalysis();
            analysis.setIR_URLTIME(StringUtils.toStringValue(row.getAs("IR_URLTIME")));
            analysis.setIR_SID(row.getAs("IR_SID"));
            analysis.setIR_URLTITLE(row.getAs("IR_URLTITLE"));
            return analysis;
        };
        JavaRDD<DhMediaAnalysis> out = SparkProcessBuilder.newProcess()
                .from(new HybaseReadAction<>(url, table, user, password, "IR_URLTIME:[20220101 TO *]", function))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByFunction这个是查询结果:" + JSONObject.toJSONString(item)));


        Function<Row, DhMediaAnalysis> function2 = row -> {
            DhMediaAnalysis lib = new DhMediaAnalysis();
            lib.setIR_URLTITLE(row.getString(0));
            lib.setIR_URLTIME(StringUtils.toStringValue(row.getLong(1)));
            lib.setIR_SID(row.getString(2));
            return lib;
        };
        out = SparkProcessBuilder.newProcess()
                .from(new HybaseReadAction<>(url, table, user, password, "IR_URLTIME:[20230101 TO *]", "IR_URLTITLE,IR_URLTIME,IR_SID", function2))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByFunction这个是IR_URLTIME:[20230101 TO *]的查询结果:" + JSONObject.toJSONString(item)));
    }

    @Test
    void doActionConvertByAS() {
        JavaRDD<DhMediaAnalysis> out = SparkProcessBuilder.newProcess()
                .from(new HybaseReadAction<>(url, table, user, password, "IR_URLTIME:[20220101 TO *]",DhMediaAnalysis.class))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByAS这个是查询结果:" + JSONObject.toJSONString(item)));

        out = SparkProcessBuilder.newProcess()
                .from(new HybaseReadAction<>(url, table, user, password, "IR_URLTIME:[20230101 TO *]", DhMediaAnalysis.class))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("doActionConvertByAS这个是IR_URLTIME:[20230101 TO *]的查询结果:" + JSONObject.toJSONString(item)));
    }
}