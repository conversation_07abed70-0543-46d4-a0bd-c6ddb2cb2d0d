package com.trs.spark.action.impl.convert.time;

import com.trs.common.utils.TimeUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.action.impl.peek.PrintPairRddItemAction;
import com.trs.spark.process.SparkProcessBuilder;
import io.vavr.Tuple2;
import org.apache.spark.api.java.JavaPairRDD;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

class TimeWindowFilterByMonthActionForCountAndTimeRangeTest extends BaseActionTest {

    @Test
    void doAction() {
        List<Tuple2<String, String>> list = new ArrayList<>(10);
        list.add(new Tuple2<>("2023-01-01 00:00:00", "a"));
        list.add(new Tuple2<>("2023-01-02 00:00:00", "a"));
        list.add(new Tuple2<>("2023-02-02 00:00:10", "a"));
        list.add(new Tuple2<>("2023-02-03 00:00:00", "a"));
        list.add(new Tuple2<>("2023-03-03 00:00:10", "a"));
        list.add(new Tuple2<>("2023-05-01 00:00:00", "a"));
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction(list))
                .convert(new TimeWindowFilterByMonthActionForCountAndTimeRange<Tuple2<String, String>>(r -> r._2, r -> TimeUtils.stringToDate(r._1), 3, 365))
                .peek(new PrintPairRddItemAction(item -> "1-每一个数据为：" + item))
                .doRun(spark);
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction(list))
                .convert(new TimeWindowFilterByMonthActionForCountAndTimeRange<Tuple2<String, String>>(r -> r._2, r -> TimeUtils.stringToDate(r._1), 3, 365, false))
                .peek(new PrintPairRddItemAction(item -> "2-每一个数据为：" + item))
                .doRun(spark);
    }
}