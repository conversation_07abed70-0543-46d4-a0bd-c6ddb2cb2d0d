package com.trs.spark.action.impl.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.trs.common.utils.TimeUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.action.impl.peek.PrintPairRddItemAction;
import com.trs.spark.process.SparkProcessBuilder;
import com.trs.spark.vo.StatusChangeMarkDemoVO;
import lombok.var;
import org.apache.spark.api.java.JavaPairRDD;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

class StatusChangeMarkActionTest extends BaseActionTest {

    @Test
    void doAction() {
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList(
                        new StatusChangeMarkDemoVO("1", "2023-06-13 10:10:10", "1"),
                        new StatusChangeMarkDemoVO("2", "2023-06-13 10:10:10", "2"),
                        new StatusChangeMarkDemoVO("1", "2023-06-10 20:10:10", "3"),
                        new StatusChangeMarkDemoVO("2", "2023-06-14 20:10:10", "4"),
                        new StatusChangeMarkDemoVO("1", "2023-06-13 10:20:10", "5"),
                        new StatusChangeMarkDemoVO("2", "2023-06-10 10:10:10", "6"),
                        new StatusChangeMarkDemoVO("1", "2023-06-12 10:10:10", "7"),
                        new StatusChangeMarkDemoVO("1", "2023-06-11 10:10:10", "8"),
                        new StatusChangeMarkDemoVO("2", "2023-06-09 10:14:10", "9"),
                        new StatusChangeMarkDemoVO("2", "2023-06-02 20:10:10", "10"),
                        new StatusChangeMarkDemoVO("1", "2023-06-12 10:10:10", "11"),
                        new StatusChangeMarkDemoVO("2", "2023-06-06 10:10:10", "12"),
                        new StatusChangeMarkDemoVO("2", "2023-06-01 20:10:10", "13"),
                        new StatusChangeMarkDemoVO("1", "2023-06-10 10:10:10", "14"),
                        new StatusChangeMarkDemoVO("2", "2023-06-14 21:10:10", "15"),
                        new StatusChangeMarkDemoVO("1", "2023-06-12 22:10:10", "16")
                )))
                .convert(new StatusChangeMarkAction<StatusChangeMarkDemoVO>(
                        r -> r.getKey(),
                        r -> TimeUtils.stringToDate(r.getTime()),
                        r -> Integer.parseInt(r.getFlag()) % 2 == 0 ? "In" : "Out"
                )).peek(new PrintPairRddItemAction<>(
                        rdd -> JSON.toJSONString(rdd, SerializerFeature.WriteDateUseDateFormat),
                        item -> System.out.println(item))
                ).doRun(spark);
    }

    @Test
    void doAction2() {
        var list = Arrays.asList(
                new StatusChangeMarkDemoVO("1", "2023-06-10 10:10:10", "1"),
                new StatusChangeMarkDemoVO("1", "2023-06-10 10:10:15", "3"),
                new StatusChangeMarkDemoVO("1", "2023-06-11 10:20:10", "5"),
                new StatusChangeMarkDemoVO("1", "2023-06-12 10:10:10", "7"),
                new StatusChangeMarkDemoVO("1", "2023-06-13 10:10:10", "8"),
                new StatusChangeMarkDemoVO("1", "2023-06-14 10:10:10", "11"),
                new StatusChangeMarkDemoVO("1", "2023-06-15 10:10:10", "14"),
                new StatusChangeMarkDemoVO("1", "2023-06-20 22:10:10", "16")
        );
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(list))
                .convert(new StatusChangeMarkAction<StatusChangeMarkDemoVO>(
                        r -> r.getKey(),
                        r -> TimeUtils.stringToDate(r.getTime()),
                        r -> Integer.parseInt(r.getFlag()) % 2 == 0 ? "In" : "Out"
                )).peek(new PrintPairRddItemAction<>(
                        rdd -> JSON.toJSONString(rdd, SerializerFeature.WriteDateUseDateFormat),
                        item -> System.out.println(item))
                ).doRun(spark);
        System.out.println("=================================");
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(list))
                .convert(new StatusChangeMarkAction<StatusChangeMarkDemoVO>(
                        r -> r.getKey(),
                        r -> TimeUtils.stringToDate(r.getTime()),
                        r -> Integer.parseInt(r.getFlag()) % 2 == 0 ? "In" : "Out",
                        true
                )).peek(new PrintPairRddItemAction<>(
                        rdd -> JSON.toJSONString(rdd, SerializerFeature.WriteDateUseDateFormat),
                        item -> System.out.println(item))
                ).doRun(spark);
    }
}