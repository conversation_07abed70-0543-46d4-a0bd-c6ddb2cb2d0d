package com.trs.spark.action.impl.from;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.peek.PrintRddAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;

class FileReadActionTest extends BaseActionTest {

    @Test
    void doAction() {
        JavaRDD<String> out = SparkProcessBuilder.newProcess()
                .from(new FileReadAction<String>("src/test/resources/num.txt"))
                .doRun(spark);
        out.collect().forEach(item -> System.out.println("这个是字符串:" + item));
        JavaRDD<Integer> out2 = SparkProcessBuilder.newProcess()
                .from(new FileReadAction<>("src/test/resources/num.txt", Integer::valueOf))
                .peek(new PrintRddAction<Integer>())
                .doRun(spark, JavaRDD.class);
        out2.collect().forEach(item -> System.out.println("这个是数值:" + item));
    }
}