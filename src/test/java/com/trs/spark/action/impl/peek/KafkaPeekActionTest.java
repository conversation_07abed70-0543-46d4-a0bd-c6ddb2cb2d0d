package com.trs.spark.action.impl.peek;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.convert.JavaRddConvertEntityAction;
import com.trs.spark.action.impl.from.JavaRddParallelizeFromAction;
import com.trs.spark.process.SparkProcessBuilder;
import com.trs.spark.vo.StatusMarkVO;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.*;

public class KafkaPeekActionTest extends BaseActionTest {

    private static final String bootstrapServers = "192.168.210.60:9092";

    @Test
    void doAction() throws InterruptedException {
        Map<String, Object> configMap = new HashMap<>();
        configMap.put("bootstrap.servers", bootstrapServers);
        configMap.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        configMap.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Arrays.asList("opop", "rrrr", "3", "899", "kkk")))
                .convert(new JavaRddConvertEntityAction<String, StatusMarkVO>(s -> StatusMarkVO.of("输入的Key" + s, s, new Date())))
                .peek(new KafkaPeekAction<StatusMarkVO>(configMap, "cd_ga_dev_60", "k1", "type1"))
                .doRun(spark);
        Thread.sleep(5000);
    }

    @Test
    void testWrite() throws InterruptedException {
        Map<String, Object> configMap = new HashMap<>();
        configMap.put("bootstrap.servers", "10.18.20.131:9092");
        configMap.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        configMap.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        SparkProcessBuilder.newProcess()
                .from(new JavaRddParallelizeFromAction<>(Collections.singletonList("{\"code\":200,\"message\":\"成功执行\",\"success\":true,\"data\":{\"jobId\":\"1\",\"jobName\":\"1\"}}")))
                .peek(new KafkaPeekAction<String>(configMap, "tb_new_collide_service", "1:1", "tb_new_collide_service2"))
                .doRun(spark);
        Thread.sleep(5000);
    }

    @Test
    void testConsumer() {
        Map<String, Object> props = new HashMap<>();
        props.put("bootstrap.servers", bootstrapServers);
        props.put("group.id", "test-group-2");
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        consumer.subscribe(Collections.singleton("cd_ga_dev_60"));
        while (true) {
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
            for (ConsumerRecord<String, String> record : records) {
                System.out.println("Received message: " + record.value());
            }
        }
    }
}
