package com.trs.spark.action.impl.convert;

import com.trs.common.utils.TimeUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.entity.StayEntity;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.junit.jupiter.api.Test;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class StayActionTest extends BaseActionTest implements Serializable {


    public List<StayEntity> getData() {
        List<StayEntity> list = new ArrayList<>();
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 08:00:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 08:10:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 08:20:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 08:30:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 08:40:00").getTime()));
        list.add(new StayEntity("2", "A",  TimeUtils.stringToDate( "2023-03-31 08:50:00").getTime()));
        list.add(new StayEntity("2", "A",  TimeUtils.stringToDate( "2023-03-31 09:00:00").getTime()));
        list.add(new StayEntity("2", "A",  TimeUtils.stringToDate( "2023-03-31 09:01:00").getTime()));
        list.add(new StayEntity("3", "A",  TimeUtils.stringToDate( "2023-03-31 08:55:00").getTime()));
        list.add(new StayEntity("3", "A",  TimeUtils.stringToDate( "2023-03-31 09:05:00").getTime()));

        list.add(new StayEntity("1", "B",  TimeUtils.stringToDate( "2023-03-31 11:10:00").getTime()));
        list.add(new StayEntity("1", "B",  TimeUtils.stringToDate( "2023-03-31 11:20:00").getTime()));
        list.add(new StayEntity("2", "B",  TimeUtils.stringToDate( "2023-03-31 09:05:00").getTime()));
        list.add(new StayEntity("2", "B",  TimeUtils.stringToDate( "2023-03-31 09:15:00").getTime()));
        list.add(new StayEntity("3", "B",  TimeUtils.stringToDate( "2023-03-31 09:30:00").getTime()));
        list.add(new StayEntity("3", "B",  TimeUtils.stringToDate( "2023-03-31 09:40:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 18:00:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 18:10:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 18:20:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 18:30:00").getTime()));
        list.add(new StayEntity("1", "A",  TimeUtils.stringToDate( "2023-03-31 18:40:00").getTime()));
//        list.add(new StayEntity("1", "B",  TimeUtils.stringToDate( "2023-03-31 08:10:00").getTime()));
//        list.add(new StayEntity("1", "C",  TimeUtils.stringToDate( "2023-03-31 08:30:00").getTime()));
//        list.add(new StayEntity("2", "A",  TimeUtils.stringToDate( "2023-03-31 08:05:00").getTime()));
//        list.add(new StayEntity("2", "B",  TimeUtils.stringToDate( "2023-03-31 08:25:00").getTime()));
//        list.add(new StayEntity("2", "B",  TimeUtils.stringToDate( "2023-03-31 08:30:00").getTime()));
//        list.add(new StayEntity("2", "B",  TimeUtils.stringToDate( "2023-03-31 08:35:00").getTime()));
//        list.add(new StayEntity("2", "C",  TimeUtils.stringToDate( "2023-03-31 08:40:00").getTime()));
//        list.add(new StayEntity("3", "A",  TimeUtils.stringToDate( "2023-03-31 08:10:00").getTime()));
//        list.add(new StayEntity("3", "B",  TimeUtils.stringToDate( "2023-03-31 08:20:00").getTime()));
//        list.add(new StayEntity("3", "C",  TimeUtils.stringToDate( "2023-03-31 08:50:00").getTime()));
//        list.add(new StayEntity( "4", "A",  TimeUtils.stringToDate( "2023-03-31 08:10:00").getTime()));
//        list.add(new StayEntity( "4", "B",  TimeUtils.stringToDate( "2023-03-31 08:30:00").getTime()));
//        list.add(new StayEntity( "4", "C",  TimeUtils.stringToDate( "2023-03-31 08:50:00").getTime()));

        return list;
    }

    @Test
    void doAction() {
        JavaSparkContext jsc = new JavaSparkContext(spark.sparkContext());
        JavaRDD<StayEntity> javaRDD = jsc.parallelize(getData());
        StayAction<StayEntity> action = new StayAction<>(10*60*1000, "objectId", "geohash", "geohash", "jdwgs84","wdwgs84", "hdsj", StayEntity.class);
        JavaRDD<StayEntity> res = action.doAction(spark, javaRDD);
        res.collect().forEach(System.out::println);
    }
}
