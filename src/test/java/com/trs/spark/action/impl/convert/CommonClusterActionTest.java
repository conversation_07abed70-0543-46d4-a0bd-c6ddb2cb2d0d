package com.trs.spark.action.impl.convert;

import com.trs.spark.BaseActionTest;
import com.trs.spark.action.impl.from.FileReadAction;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaPairRDD;
import org.junit.jupiter.api.Test;
import scala.Tuple2;

import java.util.ArrayList;

class CommonClusterActionTest extends BaseActionTest {

    @Test
    void doAction() {
        JavaPairRDD<Integer, ArrayList<Integer>> rdd =
                SparkProcessBuilder.newProcess().from(
                                new FileReadAction<>(
                                        "src/test/resources/num.txt",
                                        str -> Integer.valueOf(str)
                                )
                        ).convert(new CommonClusterAction<Integer>(2L, (p1, p2) -> p1.equals(p2)))
                        .doRun(spark);
        for (Tuple2<Integer, ArrayList<Integer>> tuple2 : rdd.collect()) {
            System.out.println("核心点为：" + tuple2._1);
            System.out.println("样本点为：" + tuple2._2);
            System.out.println("个数为：" + tuple2._2.size());
            System.out.println("==================");
        }
    }
}