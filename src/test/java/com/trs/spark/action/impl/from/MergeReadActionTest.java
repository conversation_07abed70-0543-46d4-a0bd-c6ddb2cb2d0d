package com.trs.spark.action.impl.from;

import com.trs.common.datasource.PreLoads;
import com.trs.spark.BaseActionTest;
import com.trs.spark.action.MergeReadAction;
import com.trs.common.datasource.parser.IcebergSourceUrlParser;
import com.trs.spark.entity.Person;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.junit.jupiter.api.Test;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/22 16:05
 * @version: 1.0
 */
public class MergeReadActionTest extends BaseActionTest {

    @Test
    void doActionHiveTest() {
        PreLoads.initVirtualSourceUrlLoadings(new IcebergSourceUrlParser());
        BaseSourceReadAction<Person> action = new BaseSourceReadAction<>(
                "iceberg:thrift://master.hadoop.test:9083?catalogName=cd_dev_iceberg",
                "iceberg.iceberg_dev.person1",
                "",
                "",
                Person.class
        );
        JavaRDD<Person> out = SparkProcessBuilder.newProcess()
                .from(action)
                .from(new MergeReadAction<>(action))
                .doRun(spark);
        out.foreach(r -> System.out.println(r));
    }

}
