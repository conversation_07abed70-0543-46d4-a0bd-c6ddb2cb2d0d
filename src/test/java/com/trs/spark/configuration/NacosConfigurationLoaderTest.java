package com.trs.spark.configuration;

import com.trs.spark.SparkContextHelper;
import com.trs.spark.configuration.nacosconf.NacosConfigInitializer;
import com.trs.spark.configuration.nacosconf.NacosConstants;
import com.trs.spark.constant.RunMode;
import junit.framework.Assert;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/23 15:50
 * @version: 1.0
 */
public class NacosConfigurationLoaderTest {

    /**
     * spring工程初始化测试 nacos配置信息能从bootstrap.yaml等文件读取到
     */
    @Test
    public void initializerTest() {
        NacosConfigInitializer.initConf();
        Assert.assertEquals("ES", System.getProperty("trs.db.default.repository"));
    }

    /**
     * 非spring工程初始化 支持properties和yam两种方式配置nacos信息
     */
    @Test
    public void initTest() {
        // 下面展示的是spark初始化配置 实际可使用NacosConfigInitializer.initConf("conf/nacos.yml");直接初始化配置
        Map<String, String> map = new HashMap<>(0);
        map.put(NacosConstants.KEY_NAMESPACE, "ys-local");
        map.put(NacosConstants.KEY_SERVER_ADDR, "http://************:8848");
        map.put(NacosConstants.KEY_DATA_ID, "spark-test.properties");
        SparkContextHelper.getSparkSession("applicationName", new ConfigInNacos("conf/nacos.yml", map), RunMode.DEVELOPMENT, Optional.empty());
        //NacosConfigInitializer.initConf("conf/nacos.yml");
        Assert.assertEquals("admin", Configurations.getProperty("hybase.user").orElse(""));
    }
}
