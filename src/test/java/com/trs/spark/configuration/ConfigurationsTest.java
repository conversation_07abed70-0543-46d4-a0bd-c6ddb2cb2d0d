package com.trs.spark.configuration;

import com.trs.spark.constant.RunMode;
import com.trs.spark.exception.TrsSparkException;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ConfigurationsTest {

    private WhereIsConfig<Integer> errorWhere = new WhereIsConfig<Integer>() {
        @Override
        public Integer getConfig() {
            return 0;
        }
    };

    @Test
    void inFile() {

        WhereIsConfig<String> config = Configurations.inFile("aaa");
        assertEquals("aaa", config.getConfig());

        try{
            WhereIsConfig<String> error = Configurations.inFile(null);
        }catch (NullPointerException ex) {
            assertTrue(true);
        }

    }

    @Test
    void inMap() {
        Map<String,Object> map = new HashMap<>();
        map.put("spark.home","test");

        WhereIsConfig<Map<String,Object>> config = Configurations.inMap(map);
        assertEquals("test",String.valueOf(config.getConfig().get("spark.home")));

        WhereIsConfig<Map<String,Object>> nullConfig = Configurations.inMap(null);
        assertTrue(nullConfig.getConfig().isEmpty());
    }

    @Test
    void getLoader() {

        WhereIsConfig<String> config = Configurations.inFile("aaa");
        IConfigurationLoader loader = Configurations.getLoader(config);
        assertTrue(loader instanceof FileConfigurationLoader);

        Map<String,Object> map = new HashMap<>();
        map.put("spark.home","test");

        WhereIsConfig<Map<String,Object>> mapConfig = Configurations.inMap(map);
        assertTrue(Configurations.getLoader(mapConfig) instanceof MapConfigurationLoader);

        try{
            Configurations.getLoader(errorWhere);
        }catch (TrsSparkException ex) {
            ex.printStackTrace();
            assertTrue(true);
        }

    }

    @Test
    void getProperty() {
        Map<String,Object> map = new HashMap<>();
        map.put("spark.master","test");

        WhereIsConfig<Map<String,Object>> mapConfig = Configurations.inMap(map);
        Configurations.getLoader(mapConfig).loadIntoSystem(RunMode.DEVELOPMENT);
        assertEquals("test", Configurations.getProperty("spark.master").get());
    }
}