package com.trs.spark.configuration;

import com.trs.spark.util.YamlUtils;
import junit.framework.Assert;
import org.junit.jupiter.api.Test;
import org.yaml.snakeyaml.Yaml;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/23 14:46
 * @version: 1.0
 */
public class LoadYmlTest {

    @Test
    public void loadTest() throws Exception {
        Yaml yam = new Yaml();
        Properties properties = YamlUtils.loadFromYaml(yam.load(content));
        Assert.assertEquals("**************:8848", properties.get("spring.cloud.nacos.config.server-addr"));
    }

    private static String content = "" +
            "spring:\n" +
            "  cloud:\n" +
            "# xxxxx \n" +
            "    nacos:\n" +
            "      discovery:\n" +
            "        server-addr: **************:8848\n" +
            "        namespace: 431b22de-374d-465e-b8cc-716837fe4344\n" +
            "        username: \n" +
            "        password: \n" +
            "      config:\n" +
            "        server-addr: **************:8848 #xxxx\n" +
            "        file-extension: properties\n" +
            "        namespace: 431b22de-374d-465e-b8cc-716837fe4344\n" +
            "        name: propagation-billboard.properties\n" +
            "        group: DEFAULT_GROUP\n" +
            "        username: \n" +
            "        password: \n" +
            "    stream:\n" +
            "      default-binder: rocketmq\n" +
            "  datasource:\n" +
            "    dynamic:\n" +
            "      primary: master\n" +
            "      datasource:\n" +
            "        master:\n" +
            "          url: jdbc:mysql://**************:33066/propagation_billboard?useSSL=false&serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=UTF-8&rewriteBatchedStatements=true\n" +
            "          username: root\n" +
            "          password: '!QAZ2wsx1234'\n" +
            "          driver-class-name: com.mysql.cj.jdbc.Driver\n" +
            "        starrocks:\n" +
            "          url: jdbc:mysql://**************:18917/propagation_billboard?useSSL=false&serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=UTF-8&rewriteBatchedStatements=true\n" +
            "          username: root\n" +
            "          password: \n" +
            "          driver-class-name: com.mysql.cj.jdbc.Driver\n" +
            "    hikari:\n" +
            "      connection-init-sql: SET NAMES utf8mb4\n" +
            "  main:\n" +
            "    allow-bean-definition-overriding: true\n" +
            "  redis:\n" +
            "    masterName: \n" +
            "    nodes: ***************:6379\n" +
            "    pwd: \n" +
            "dubbo:\n" +
            "  application:\n" +
            "    logger: slf4j\n" +
            "  protocol:\n" +
            "    name: dubbo\n" +
            "  registry:\n" +
            "    address: zookeeper://***************:2181\n" +
            "    timeout: 20000\n" +
            "\n" +
            "logging:\n" +
            "  config: classpath:logback-spring.xml";
}
