package com.trs.spark.configuration;

import com.trs.spark.constant.RunMode;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class FileConfigurationLoaderTest {

    @Test
    void loadIntoSystem() {
        WhereIsConfig<String> filePath = Configurations.inFile("conf");
        try {
            //当前文件夹下
            Configurations.getLoader(filePath).loadIntoSystem(RunMode.DEVELOPMENT);
            assertTrue(true);
        }catch(Exception ex) {
            ex.printStackTrace();
        }

        //在单元测试中，如果使用DEPLOY模式将报错
        try{
            Configurations.getLoader(filePath).loadIntoSystem(RunMode.DEPLOY);
        }catch (Exception ex) {
            assertTrue(true);
            ex.printStackTrace();
        }
    }
}