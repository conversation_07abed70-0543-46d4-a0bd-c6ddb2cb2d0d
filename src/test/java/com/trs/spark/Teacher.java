package com.trs.spark;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
public class Teacher implements Serializable {
    private Integer id;
    private String name;
    private Integer age;
    private Double high;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Double getHigh() {
        return high;
    }

    public void setHigh(Double high) {
        this.high = high;
    }
}
