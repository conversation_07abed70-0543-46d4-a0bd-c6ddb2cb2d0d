package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.BaseActionTest;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.junit.jupiter.api.Test;

/**
 * *@author:wen.wen
 * *@create 2023-03-20 12:57
 **/
public class JdbcSourceTest extends BaseActionTest {

    @Test
    public void test() throws ClassNotFoundException {
        JdbcSource jdbcSource = new JdbcSource();
        SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver("*********************************", "root", "root").get();
        //读取数据
        Dataset<Row> rowDataset = jdbcSource.loadDataFromSource(spark, sourceDriver, "city");
        assert true;
        //插入数据
        jdbcSource.writeDataToSource(spark, sourceDriver, "city_1", rowDataset, SaveMode.Overwrite);
        assert true;
    }
}
