package com.trs.spark.datasource;

import com.alibaba.fastjson.JSON;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.datasource.condition.SearchParams;
import junit.framework.Assert;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.junit.jupiter.api.Test;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/20 10:35
 * @version: 1.0
 */
public class RedisDataSourceTest extends BaseDataSourceTest {

    @Override
    SourceDriver sourceDriver() {
        try {
            return SourceDriverUtils.createSourceDriver("redis://127.0.0.1:6379,*********:6379", "", "").getOrNPException("未能获取到连接信息");
        } catch (Exception e) {
            return null;
        }
    }

    @Test
    public void redisSourceTest() {
        RedisSource source = new RedisSource();
        String table = "person";
        // 写入测试, 无法断言 所以通过读取来断言是否写入成功
        Person zs = new Person("511", "张三", 25);
        Person ls = new Person("512", "李四", 22);
        Dataset<Row> df = spark.createDataFrame(Arrays.asList(zs, ls), Person.class);
        source.writeDataToSource(spark, sourceDriver(), table, df, SaveMode.Overwrite);

        // 读取测试
        System.out.println("读取测试");
        Dataset<Row> dataset = source.loadDataFromSource(spark, sourceDriver(), table);
        Assert.assertEquals("结果不正确", 2, dataset.count());
        dataset
            .toJavaRDD()
            .filter(r -> "李四".equals(r.getAs("name")))
            .foreach(r -> {
                System.out.println(r.json());
                Assert.assertEquals("id丢失", "512", r.getAs("idCard"));
            });

        // 读取添加条件测试
        System.out.println("读取添加条件测试");
        SearchParams searchParams = new SearchParams();
        searchParams.setSelectFields("idCard;name");
        searchParams.setWhere("age = 25");
        Dataset<Row> datasetWithParam = source.loadDataFromSource(spark, sourceDriver(), table, searchParams);
        datasetWithParam.show();
        Assert.assertEquals("结果不正确", 1, datasetWithParam.count());
        datasetWithParam
                .toJavaRDD()
                .foreach(r -> {
                    String json = r.json();
                    System.out.println(json);
                    Person person = JSON.parseObject(json, Person.class);
                    Assert.assertTrue("过滤字段失败", Objects.isNull(person.getAge()));
                });
    }

    public static class Person implements Serializable {

        private String idCard;

        private String name;

        private Integer age;

        public Person() {
        }

        public Person(String idCard, String name, Integer age) {
            this.idCard = idCard;
            this.name = name;
            this.age = age;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }
    }
}
