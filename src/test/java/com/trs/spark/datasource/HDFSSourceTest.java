package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.entity.Person;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * *@author:wen.wen
 * *@create 2023-03-20 12:57
 **/
public class HDFSSourceTest extends BaseActionTest {

    @Test
    public void test() {
        HDFSSource hdfsSource = new HDFSSource();
        SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver("hdfs://127.0.0.1:9000", "", "").get();
        // 读取数据
        String[] data = new String[]{"<PERSON>", "<PERSON>", "<PERSON>"};
        // 将字符串数组转换为Dataset
        Dataset<String> dataset = spark.createDataset(Arrays.asList(data), Encoders.STRING());
        dataset.show();
        assert true;
        // 插入数据
        hdfsSource.writeDataToSource(spark, sourceDriver, "/hdfs2.text", dataset, SaveMode.Overwrite);
        assert true;
    }

    @Test
    public void testV2() {
        HDFSSource source = new HDFSSource();
        SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver("hdfs://10.18.20.101:8020", "", "").get();
        // 写入测试
        System.out.println("写入测试");
        // 创建一个字符串数组
        String[] data = new String[]{"Alice", "Bob", "Charlie"};
        // 将字符串数组转换为Dataset
        Dataset<String> dataset = spark.createDataset(Arrays.asList(data), Encoders.STRING());
        source.writeDataToSource(spark, sourceDriver, "/user/trs/hdfs_person_test.text", dataset, SaveMode.Overwrite);
        // 读取测试
        Dataset<Row> rowDataset = source.loadDataFromSource(spark, sourceDriver, "/user/trs/hdfs_person_test.text");
        rowDataset.show();
    }
}
