package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.VirtualUrlSourceDirver;
import com.trs.spark.BaseActionTest;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * *@author:wen.wen
 * *@create 2023-03-20 11:18
 **/
public class HybaseSourceTest extends BaseActionTest {

    @Test
    public void test() throws ClassNotFoundException {
        HybaseSource hybaseSource = new HybaseSource();
        SourceDriver sourceDriver = new VirtualUrlSourceDirver("hybase://192.168.210.57:5555", "admin", "trsadmin");
        Dataset<Row> rowDataset = hybaseSource.loadDataFromSource(spark, sourceDriver, "originalData.dh_media_analysis3");
        assert true;
        hybaseSource.writeDataToSource(spark, sourceDriver, "originalData.dh_media_analysis_test_20230320", rowDataset, SaveMode.Overwrite);
        assert true;
    }
}
