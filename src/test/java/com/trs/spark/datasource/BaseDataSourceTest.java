package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import org.apache.spark.sql.SparkSession;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;

public abstract class BaseDataSourceTest {

    public static SparkSession spark;

    @BeforeAll
    static void init() {
        System.out.println("开始初始化SparkSession");
        if (spark == null) {
            spark = SparkSession
                    .builder()
                    .enableHiveSupport()
                    .appName("test")
                    .master("local[4]")
                    .getOrCreate();
        }
    }

    @AfterAll
    static void close() {
        System.out.println("开始销毁SparkSession");
        if (spark != null) {
            spark.stop();
        }
    }

    abstract SourceDriver sourceDriver();
}
