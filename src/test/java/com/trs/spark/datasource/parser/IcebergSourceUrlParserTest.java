package com.trs.spark.datasource.parser;

import com.trs.common.base.Option;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.common.datasource.parser.SourceUrlParser;
import junit.framework.Assert;
import org.junit.jupiter.api.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.trs.common.datasource.parser.IcebergSourceUrlParser;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/22 14:35
 * @version: 1.0
 */
public class IcebergSourceUrlParserTest {

    Pattern pattern = Pattern.compile("(?<url>(((thrift)|(hdfs))://[^?]+))((\\?(?<params>(.*))))?");

    @Test
    public void sourceDriverTest() {
        Option<SourceDriver> sourceDriver = SourceDriverUtils.createSourceDriver("iceberg:thrift://master.hadoop.test:9083", "", "");
        Assert.assertTrue(!sourceDriver.isEmpty());
    }

    @Test
    public void patternTest() {
        Matcher matcher = pattern.matcher("thrift://127.0.0.1:9083/db?catalogType=hive&catalogName=cd_dev_iceberg");
        System.out.println(matcher.find());
        String url = matcher.group("url");
        String params = matcher.group("params");
        System.out.println();
    }

    @Test
    public void test() {
        IcebergSourceUrlParser parser = new IcebergSourceUrlParser();
        SourceUrlParser res = parser.parse("thrift://127.0.0.1:9083/db?type=hive");
        System.out.println();
    }
}
