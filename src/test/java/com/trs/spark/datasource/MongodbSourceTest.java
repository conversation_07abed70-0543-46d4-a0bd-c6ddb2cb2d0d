package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.datasource.condition.SearchParams;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.junit.jupiter.api.Test;

/**
 * *@author:wen.wen
 * *@create 2023-03-20 12:57
 **/
public class MongodbSourceTest extends BaseActionTest {

    @Test
    public void test() {
        MongodbSource mongodbSource = new MongodbSource();
        SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver("mongodb://192.168.110.172:27017;192.168.110.186:27017;192.168.110.183:27017", "", "").get();
        //读取数据
        SearchParams searchParams = new SearchParams();
        searchParams.setSelectFields("a;uuid");
        searchParams.setWhere("a = 2");
        Dataset<Row> rowDataset = mongodbSource.loadDataFromSource(spark, sourceDriver, "test.demo", searchParams);
        rowDataset.show();
        assert true;
        //插入数据
        mongodbSource.writeDataToSource(spark, sourceDriver, "test.demo", rowDataset, SaveMode.Overwrite);
        assert true;
    }
}
