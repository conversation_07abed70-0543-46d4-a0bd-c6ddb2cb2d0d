package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.datasource.condition.SearchParams;
import junit.framework.Assert;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.junit.jupiter.api.Test;

import java.io.Serializable;
import java.util.Arrays;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/21 11:45
 * @version: 1.0
 */
public class IcebergDataSourceTest extends BaseDataSourceTest {

    private static SourceDriver sourceDriver;

    /**
     * 默认走hive方式
     *
     * @return
     */
    @Override
    SourceDriver sourceDriver() {
        SourceDriver sourceDriver1 = SourceDriverUtils.createSourceDriver("iceberg:thrift://master.hadoop.test:9083", "", "").get();
        return sourceDriver1;
    }

    /**
     * 基于hive的iceberg读写实现的测试用例
     */
    @Test
    public void icebergHiveCatalogTest() {
        sourceDriver = SourceDriverUtils.createSourceDriver("iceberg:thrift://master.hadoop.test:9083", "", "").get();
        //catalog.db.table
        String tbName = "iceberg_hive.iceberg_dev.person1";
        icebergTest(tbName);
    }

    /**
     * 基于hadoop的iceberg读写实现的测试用例
     */
    @Test
    public void icebergHadoopCatalogTest() {
        sourceDriver = SourceDriverUtils.createSourceDriver("iceberg:hdfs://master.hadoop.test:8020/user/hive/warehouse/", "", "").get();
        //catalog.db.table
        String tbName = "iceberg_hadoop.iceberg_dev_hadoop.person";
        icebergTest(tbName);
    }

    /**
     * 数据写入：需要预先新建表：spark.sqlContext().sql("create table catalog.mydb.person (idCard string,name string,age int) using iceberg");
     */
    private void icebergTest(String tbName) {
        IcebergSource source = new IcebergSource();

        // 写入测试
        System.out.println("写入测试");
        Person zs = new Person("513_" + System.currentTimeMillis(), "black\r\nxxxx\n aaaaa", 12);
//        Person ls = new Person("514_" + System.currentTimeMillis(), "李四1", 22);
        Dataset<Row> df = spark.createDataFrame(Arrays.asList(zs), Person.class);
        source.writeDataToSource(spark, sourceDriver(), tbName, df, SaveMode.Append);

        // 读取测试
        System.out.println("读取测试");
        Dataset<Row> dataset = source.loadDataFromSource(spark, sourceDriver, tbName, new SearchParams());
        dataset.show();

        // 读取添加条件测试
        System.out.println("读取添加条件测试");
        SearchParams searchParams = new SearchParams();
        searchParams.setSelectFields("idCard;age");
        searchParams.setWhere("age > 22");
        Dataset<Row> datasetWithParam = source.loadDataFromSource(spark, sourceDriver, tbName, searchParams);
        datasetWithParam.show();
        datasetWithParam
                .toJavaRDD()
                .foreach(r -> {
                    Integer age = r.getAs("age");
                    Assert.assertTrue("where 条件失效", age > 22);
                });
    }

    public static class Person implements Serializable {

        private String idCard;

        private String name;

        private Integer age;

        public Person() {
        }

        public Person(String idCard, String name, Integer age) {
            this.idCard = idCard;
            this.name = name;
            this.age = age;
        }

        public String getIdCard() {
            return idCard;
        }

        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }
    }

    @Test
    public void sqlTest() {
        IcebergSource source = new IcebergSource();
        sourceDriver = SourceDriverUtils.createSourceDriver("iceberg:thrift://master.hadoop.test:9083", "", "").get();
        //catalog.db.table
        String tbName = "iceberg.iceberg_dev.tb_gather_middle_result";
        SearchParams searchParams = new SearchParams();
        searchParams.setSql("select * from (SELECT *, ROW_NUMBER() OVER (PARTITION BY objectid ORDER BY activityTime DESC) as row_num" +
                " FROM iceberg.iceberg_dev.tb_gather_middle_result)" +
                " where row_num=1");
        searchParams.setSelectFields("objectId");
        searchParams.setWhere("objectId>1");
        Dataset<Row> dataset = source.loadDataFromSource(spark, sourceDriver, tbName, searchParams);
        dataset.show();
    }
}
