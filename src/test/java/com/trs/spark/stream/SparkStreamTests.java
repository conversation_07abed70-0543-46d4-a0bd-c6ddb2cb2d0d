package com.trs.spark.stream;

import com.alibaba.fastjson.JSONObject;
import com.trs.spark.entity.DataBaseInfo;
import com.trs.spark.entity.PersistMessageDTO;
import com.trs.spark.entity.RecordInfo;
import com.trs.spark.util.DatasetConvertUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.spark.SparkConf;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.VoidFunction;
import org.apache.spark.api.java.function.VoidFunction2;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.hive.HiveContext;
import org.apache.spark.streaming.Durations;
import org.apache.spark.streaming.Time;
import org.apache.spark.streaming.api.java.JavaInputDStream;
import org.apache.spark.streaming.api.java.JavaStreamingContext;
import org.apache.spark.streaming.kafka010.ConsumerStrategies;
import org.apache.spark.streaming.kafka010.KafkaUtils;
import org.apache.spark.streaming.kafka010.LocationStrategies;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.stream.Collectors;

import static org.apache.hadoop.util.Shell.SYSPROP_HADOOP_HOME_DIR;

/**
 * *@author:wen.wen
 * *@create 2023-07-13 09:19
 **/
public class SparkStreamTests {

    @Test
    public void test() throws InterruptedException {

        System.setProperty(SYSPROP_HADOOP_HOME_DIR, "hdfs://**************:9000/");

        SparkConf sparkConf = new SparkConf()
                .setMaster("local[*]")
                .setAppName("SparkStreamToHive");
        Collection<String> topicsSet = new HashSet<>(Arrays.asList("ww_test".split(",")));
        //构建JavaStreamingContext
        JavaStreamingContext streamingContext = new JavaStreamingContext(sparkConf, Durations.seconds(30));
        //通过KafkaUtils.createDirectStream(...)获得kafka数据，kafka相关参数由kafkaParams指定
        JavaInputDStream<ConsumerRecord<String, String>> lines = KafkaUtils.createDirectStream(
                streamingContext,
                LocationStrategies.PreferConsistent(),
                ConsumerStrategies.Subscribe(topicsSet, kafkaParams()));

        JavaSparkContext javaSparkContext = streamingContext.sparkContext();
        // 在窗口操作中处理数据流
        lines.foreachRDD((VoidFunction2<JavaRDD<ConsumerRecord<String, String>>, Time>) (v1, v2) -> {
            if (!v1.isEmpty()) {
                // 使用广播变量来共享 persistMessageDTOList
                Broadcast<List<PersistMessageDTO>> broadcastPersistMessageDTOList = JavaSparkContext.fromSparkContext(v1.context())
                        .broadcast(new ArrayList<>());
                v1.foreach((VoidFunction<ConsumerRecord<String, String>>) record -> broadcastPersistMessageDTOList.getValue().add(JSONObject.parseObject(record.value(), PersistMessageDTO.class)));

                List<PersistMessageDTO> persistMessageDTOList = broadcastPersistMessageDTOList.getValue();
                Map<String, List<PersistMessageDTO>> map = persistMessageDTOList.stream()
                        .collect(Collectors.groupingBy((p) -> JSONObject.toJSONString(p.getDataBaseInfo())));
                System.out.println(JSONObject.toJSONString(persistMessageDTOList));
                map.forEach((key, persistMessageDTOS) -> {
                    DataBaseInfo dataBaseInfo = persistMessageDTOS.get(0).getDataBaseInfo();
                    String hiveUrl = dataBaseInfo.getHost();
                    SparkSession sparkSession = SparkSession.builder()
                            .sparkContext(javaSparkContext.sc())
                            .config("hive.metastore.uris", String.format("thrift://%s:9083", hiveUrl))
                            .config("spark.sql.warehouse.dir", "/user/hive/warehouse/")
                            .config("hive.metastore.execute.setugi", "true")
                            .config("spark.hadoop.fs.defaultFS", "hdfs://**************:9000")
//                .config("hive.metastore.user", "root")
//                .config("hive.metastore.password", "zE9Ll6c!nZm4")
//                .enableHiveSupport()
                            .getOrCreate();
                    //设置表信息
                    String table = dataBaseInfo.getDbName() + "." + dataBaseInfo.getTableName();
                    //获取字段的StructType
                    List<Map<String, Object>> records = persistMessageDTOS.stream().map(PersistMessageDTO::getRecordInfo).map(RecordInfo::getFieldValueMap).collect(Collectors.toList());
                    Dataset dataFrame = DatasetConvertUtils.javaMap2Dataset(sparkSession, records);
                    dataFrame.show();
//                    dataFrame.write().mode(SaveMode.Append).saveAsTable(table);
                });
            }
        });
        streamingContext.start();
        streamingContext.awaitTermination();
    }

    private static Map<String, Object> kafkaParams() {
        String brokers = "192.168.211.90:19092";
        //kafka相关参数，必要！缺了会报错
        Map<String, Object> kafkaParams = new HashMap<>();
        kafkaParams.put("metadata.broker.list", brokers);
        kafkaParams.put("bootstrap.servers", brokers);
        kafkaParams.put("group.id", "group-ww1");
        kafkaParams.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        kafkaParams.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        kafkaParams.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        kafkaParams.put("auto.offset.reset", "earliest");
        return kafkaParams;
    }
}
