package com.trs.spark.rdd;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.spark.BaseActionTest;
import com.trs.spark.datasource.BaseSource;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.entity.DhMediaAnalysis;
import com.trs.spark.function.CheckedFunction;
import com.trs.spark.function.Function;
import com.trs.spark.util.SourceUtils;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

public class ExtensionRddTest extends BaseActionTest {

    private static String url = "hybase://192.168.210.57:5555";
    private static String table = "originalData.dh_media_analysis3";

    private static String user = "admin";
    private static String password = "trsadmin";

    private static SourceDriver sourceDriver;

    private static BaseSource baseSource;

    private static SearchParams searchParams;

    static CheckedFunction<Row, DhMediaAnalysis> function;

    static Function<DhMediaAnalysis, String> makeStandardKey;
    static Function<DhMediaAnalysis, List<String>> mutiKeyMaker;
    static void initV2() {
        sourceDriver = SourceDriverUtils.createSourceDriver(url, user, password).get();
        baseSource = SourceUtils.makeSource(sourceDriver);
        searchParams = new SearchParams();
        searchParams.setWhere("IR_URLTIME:[20220101 TO *]");
        function = row -> {
            DhMediaAnalysis analysis = new DhMediaAnalysis();
            long time = row.getAs("IR_URLTIME");
            analysis.setIR_URLTIME(StringUtils.toStringValue(time));
            analysis.setIR_SID(row.getAs("IR_SID"));
            analysis.setIR_URLTITLE(row.getAs("IR_URLTITLE"));
            return analysis;
        };
        makeStandardKey = (dhMediaAnalysis) -> TimeUtils.stringToString(dhMediaAnalysis.getIR_URLTIME(), TimeUtils.YYYYMMDD);
        mutiKeyMaker = (dhMediaAnalysis) -> {
            List<String> keys = new ArrayList<>();
            String key = TimeUtils.stringToString(dhMediaAnalysis.getIR_URLTIME(), TimeUtils.YYYYMMDD);
            keys.add(key);
            keys.add(key + "_2");
            return keys;
        };
    }


    @Test
    void optMapTest() {
        JavaRDD<DhMediaAnalysis> dhMediaAnalysisJavaRDD = getRDD();
        Dataset<DhMediaAnalysis> dataset = spark.createDataset(dhMediaAnalysisJavaRDD.rdd(), Encoders.bean(DhMediaAnalysis.class));
        dataset.show();
        assert true;
    }

    @Test
    void flatMapToPair() {
        ExtensionRdd.flatMapToPair(getRDD(), makeStandardKey).foreach((listTuple2) -> {
            String key = listTuple2._1();
            DhMediaAnalysis value = listTuple2._2();
            System.out.println(key + "=" + JSONObject.toJSONString(value));
        });
    }

    @Test
    void flatMapToPairMutiKey() {
        Function<DhMediaAnalysis, List<String>> mutiKeyMaker = (dhMediaAnalysis) -> {
            List<String> keys = new ArrayList<>();
            String key = TimeUtils.stringToString(dhMediaAnalysis.getIR_URLTIME(), TimeUtils.YYYYMMDD);
            keys.add(key);
            keys.add(key + "_2");
            return keys;
        };
        ExtensionRdd.flatMapToPairMutiKey(getRDD(), mutiKeyMaker).foreach((listTuple2) -> {
            String key = listTuple2._1();
            DhMediaAnalysis value = listTuple2._2();
            System.out.println(key + "=" + JSONObject.toJSONString(value));
        });
    }

    @Test
    void optGroupByWithCustomizeSeqOp() {
        JavaRDD<DhMediaAnalysis> dhMediaAnalysisJavaRDD = getRDD();
        JavaPairRDD<String, List<DhMediaAnalysis>> javaPairRDD = ExtensionRdd.optGroupByWithCustomizeSeqOp(dhMediaAnalysisJavaRDD, makeStandardKey);
        javaPairRDD.foreach((listTuple2) -> {
            String key = listTuple2._1();
            List<DhMediaAnalysis> value = listTuple2._2();
            System.out.println(key + "=" + JSONObject.toJSONString(value));
        });
    }

    @Test
    void testOptGroupByWithCustomizeSeqOp() {
        JavaRDD<DhMediaAnalysis> dhMediaAnalysisJavaRDD = getRDD();
        JavaPairRDD<String, List<DhMediaAnalysis>> javaPairRDD = ExtensionRdd.optGroupByWithCustomizeSeqOp(
                dhMediaAnalysisJavaRDD,
                makeStandardKey,
                (list, dhMediaAnalysis) -> {
                    if (!StringUtils.isNullOrEmpty(dhMediaAnalysis.getIR_URLTITLE()) && dhMediaAnalysis.getIR_URLTITLE().contains("西藏")) {
                        list.add(dhMediaAnalysis);
                    }
                    return list;
                });
        javaPairRDD.foreach((listTuple2) -> {
            String key = listTuple2._1();
            List<DhMediaAnalysis> value = listTuple2._2();
            System.out.println(key + "=" + JSONObject.toJSONString(value));
        });
    }

    private JavaRDD<DhMediaAnalysis> getRDD() {
        initV2();
        Dataset<Row> rowDataset = baseSource.loadDataFromSource(spark, sourceDriver, table, searchParams);
        return ExtensionRdd.optMap(rowDataset.javaRDD(), function);
    }

}