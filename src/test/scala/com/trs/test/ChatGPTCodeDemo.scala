package com.trs.test

import org.apache.spark.ml.clustering.KMeans
import org.apache.spark.ml.feature.VectorAssembler
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.types._
import org.junit.jupiter.api.Test

class ChatGPTCodeDemo {

  @Test
  def testCode() = {
    val spark = SparkSession
      .builder
      .master("local[2]")
      .enableHiveSupport()
      .appName("ChatGPTCodeDemo")
      .getOrCreate()
    import spark.implicits._

    val df = spark
      .read
      .csv("src/test/resources/a.csv")
      .toDF("vehicle_id", "longitude", "latitude", "pass_time")
    val cols = df.columns.map(f => {
      f match {
        case "latitude" => df(f).cast(DoubleType)
        case "longitude" => df(f).cast(DoubleType)
        case _ => df(f).cast(StringType)
      }
    })
    val data = df.select(cols: _*)
    data.show()
    // 转换数据格式
    val assembler = new VectorAssembler().setInputCols(Array("longitude", "latitude")).setOutputCol("features")
    val transformedData = assembler.transform(data)

    // 使用K-Means算法进行聚类
    val kmeans = new KMeans().setK(6).setSeed(1L)
    val model = kmeans.fit(transformedData)

    // 得到聚类结果
    val predictions = model.transform(transformedData)
    predictions.show()

    // 计算每个点到其他点的时间间隔，如果超过阈值则认为车辆在该区域逗留
    val threshold = 30 * 60 // 30分钟
    val stayedData = predictions.rdd.groupBy(row => row.getAs[Int]("prediction"))
      .flatMap(group => {
        val rows = group._2.toArray.sortBy(_.getAs[String]("pass_time"))
        var result = List[(String, Double)]()
        for (i <- 0 until rows.length - 1) {
          val currentRow = rows(i)
          val nextRow = rows(i + 1)
          val currentPassTime = currentRow.getAs[String]("pass_time").toLong
          val nextPassTime = nextRow.getAs[String]("pass_time").toLong
          val timeInterval = (nextPassTime - currentPassTime) / 1000
          if (timeInterval > threshold) {
            result = (currentRow.getAs[String]("vehicle_id"), timeInterval.toDouble) :: result
          }
        }
        result
      }).toDF("vehicle_id", "stay_time")
    stayedData.show()
  }
}
