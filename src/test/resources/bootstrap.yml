spring:
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        namespace: 431b22de-374d-465e-b8cc-716837fe4344
        username:
        password:
      config:
        server-addr: **************:8848
        file-extension: properties
        namespace: 431b22de-374d-465e-b8cc-716837fe4344
        name: propagation-billboard.properties
        group: DEFAULT_GROUP
        username:
        password:
    stream:
      default-binder: rocketmq
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *****************************************************************************************************************************************************************
          username: root
          password: '!QAZ2wsx1234'
          driver-class-name: com.mysql.cj.jdbc.Driver
        starrocks:
          url: *****************************************************************************************************************************************************************
          username: root
          password:
          driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-init-sql: SET NAMES utf8mb4
  main:
    allow-bean-definition-overriding: true
  redis:
    masterName:
    nodes: ***************:6379
    pwd:
dubbo:
  application:
    logger: slf4j
  protocol:
    name: dubbo
  registry:
    address: zookeeper://***************:2181
    timeout: 20000

logging:
  config: classpath:logback-spring.xml