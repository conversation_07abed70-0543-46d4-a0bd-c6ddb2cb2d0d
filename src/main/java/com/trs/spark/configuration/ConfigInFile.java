package com.trs.spark.configuration;

import com.trs.common.base.PreConditionCheck;

/**
 * <AUTHOR>
 */
public class ConfigInFile implements WhereIsConfig<String> {

    private final String propertyPath;

    public ConfigInFile(String property) {
        this.propertyPath = PreConditionCheck.checkNotNull(property);
    }

    @Override
    public String getConfig() {
        return this.propertyPath;
    }
}
