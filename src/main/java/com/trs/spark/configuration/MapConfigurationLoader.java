package com.trs.spark.configuration;

import com.trs.common.base.PreConditionCheck;
import com.trs.spark.constant.RunMode;
import com.trs.spark.exception.TrsSparkException;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class MapConfigurationLoader implements IConfigurationLoader {

    private Map<String, Object> propertiesMap;

    public MapConfigurationLoader(Map<String, Object> properties) {
        this.propertiesMap = PreConditionCheck.checkNotNull(properties);
    }

    /**
     * 加到配置到系统变量当中，系统变量可能来自配置文件，也可能来自Map
     *
     * @param mode
     * @throws TrsSparkException
     */
    @Override
    public void loadIntoSystem(RunMode mode) throws TrsSparkException {
        if (propertiesMap.isEmpty()) {
            return;
        }
        propertiesMap.forEach((key, value) -> {
            System.setProperty(key, String.valueOf(value));
        });
        //这里执行一次validate，确保核心参数在其中
        validate();
    }
}
