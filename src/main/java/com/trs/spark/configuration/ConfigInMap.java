package com.trs.spark.configuration;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ConfigInMap implements WhereIsConfig<Map<String, Object>> {

    private final Map<String, Object> config;

    public ConfigInMap(Map<String, Object> property) {
        if (property != null && !property.isEmpty()) {
            config = property;
        } else {
            config = Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> getConfig() {
        return config;
    }
}
