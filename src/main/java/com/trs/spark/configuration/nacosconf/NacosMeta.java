package com.trs.spark.configuration.nacosconf;

/**
 * @author: zhang.wenquan
 * @description: nacos元数据
 * @date: 2023/3/23 11:03
 * @version: 1.0
 */
public class NacosMeta {

    private String serverAddr;

    private String nameSpace;

    private String dataId;

    private String group;

    private String username;

    private String password;

    /**
     * 加载方式 HTTP NACOS (目前仅支持HTTP)
     */
    private String loadWay;

    public NacosMeta() {
    }

    public String getServerAddr() {
        return serverAddr;
    }

    public String getNameSpace() {
        return nameSpace;
    }

    public String getDataId() {
        return dataId;
    }

    public String getGroup() {
        return group;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public void setServerAddr(String serverAddr) {
        this.serverAddr = serverAddr;
    }

    public void setNameSpace(String nameSpace) {
        this.nameSpace = nameSpace;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getLoadWay() {
        return loadWay;
    }

    public void setLoadWay(String loadWay) {
        this.loadWay = loadWay;
    }
}
