package com.trs.spark.configuration.nacosconf;

/**
 * @version 1.0
 * @description NacosApi <BR>
 * <p>
 * @author：zhang.wenquan
 * @create on 2021/9/7 16:02
 * @company TRS信息技术有限公司
 */
public interface INacosService {

    /**
     * Description:
     *
     * @param serverAddr
     * @return boolean
     */
    boolean isHealth(String serverAddr);

    /**
     * Get config.
     *
     * @param serverAddr 服务器地址
     * @param nameSpace  命名空间
     * @param dataId     dataId
     * @param group      group
     * @param timeoutMs  read timeout
     * @return config value
     * @throws Exception ex
     */
    String getConfig(String serverAddr, String nameSpace, String dataId, String group, long timeoutMs) throws Exception;

    /**
     * Publish config.
     *
     * @param serverAddr 服务器地址
     * @param nameSpace  命名空间
     * @param dataId     dataId
     * @param group      group
     * @param content    content
     * @param type       config type
     * @return Whether publish
     * @throws Exception ex
     */
    boolean publishConfig(String serverAddr, String nameSpace, String dataId, String group, String content, String type) throws Exception;
}
