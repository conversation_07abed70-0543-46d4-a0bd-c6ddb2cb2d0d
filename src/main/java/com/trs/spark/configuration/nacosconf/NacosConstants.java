package com.trs.spark.configuration.nacosconf;


/**
 * @description： 常量类
 * @author：zhang.wenquan
 * @create on 2021/8/12
 */
public class NacosConstants {

    public static final String KEY_SERVER_ADDR = "serverAddr";
    public static final String KEY_NAMESPACE = "namespace";
    public static final String KEY_NAME = "name";
    public static final String KEY_DATA_ID = "dataId";
    public static final String KEY_GROUP = "group";
    public static final String KEY_USERNAME = "username";
    public static final String KEY_PASSWORD = "password";
    public static final String KEY_LOADWAY = "loadWay";

    public static final String NULL_STR = "NULL";

    /**
     * yml文件后缀
     */
    public static final String YML = "yml";

    /**
     * yaml文件后缀
     */
    public static final String YAML = "yaml";

    /**
     * properties文件后缀
     */
    public static final String PROPERTIES = "properties";

    public static final String DEFAULT_GROUP = "DEFAULT_GROUP";

    /**
     * 默认的超时时间
     */
    public static final long DEFAULT_TIME_OUT = 3000L;

    /**
     * 服务器健康的标识
     */
    public static final String SERVER_IS_HEALTH = "UP";

    public static final String LOAD_HTTP = "http";

    public enum MetaInfo {

        /**
         * 配置
         */
        CONFIG(
                "spring.cloud.nacos.config.server-addr",
                "spring.cloud.nacos.config.file-extension",
                "spring.cloud.nacos.config.namespace",
                "spring.cloud.nacos.config.name",
                "spring.cloud.nacos.config.group",
                "spring.cloud.nacos.config.username",
                "spring.cloud.nacos.config.password"
        );
        private String addrPath;

        private String extensionPath;

        private String namespacePath;

        private String namePath;

        private String groupPath;

        private String usernamePath;

        private String passwordPath;

        MetaInfo(String addrPath, String extensionPath, String namespacePath, String namePath, String groupPath, String usernamePath, String passwordPath) {
            this.addrPath = addrPath;
            this.extensionPath = extensionPath;
            this.namespacePath = namespacePath;
            this.namePath = namePath;
            this.groupPath = groupPath;
            this.usernamePath = usernamePath;
            this.passwordPath = passwordPath;
        }

        public String getAddrPath() {
            return addrPath;
        }

        public String getExtensionPath() {
            return extensionPath;
        }

        public String getNamespacePath() {
            return namespacePath;
        }

        public String getNamePath() {
            return namePath;
        }

        public String getGroupPath() {
            return groupPath;
        }

        public String getUsernamePath() {
            return usernamePath;
        }

        public String getPasswordPath() {
            return passwordPath;
        }
    }
}
