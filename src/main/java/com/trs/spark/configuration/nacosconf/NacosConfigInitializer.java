package com.trs.spark.configuration.nacosconf;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/23 15:37
 * @version: 1.0
 */
public class NacosConfigInitializer {

    private static final Logger log = LoggerFactory.getLogger(NacosConfigInitializer.class);


    /**
     * spring工程无需填写资源路径，直接加载nacos配置
     */
    public static void initConf() {
        initConf(null, Collections.emptyMap());
    }

    /**
     * 非spring工程需要自定资源路径（即nacos信息配置在哪儿的， 如application.properties）
     *
     * @param resourcePath 参数
     * @param map          参数
     */
    public static void initConf(@Nullable String resourcePath, Map<String, String> map) {
        try {
            NacosMeta config = Objects.isNull(resourcePath) ? NacosMetaSupplier.get() : NacosMetaSupplier.get(resourcePath);
            if (Objects.nonNull(map)) {
                map.forEach((k, v) -> {
                    switch (StringUtils.showEmpty(k)) {
                        case NacosConstants.KEY_SERVER_ADDR:
                            config.setServerAddr(v);
                            break;
                        case NacosConstants.KEY_NAMESPACE:
                            config.setNameSpace(v);
                            break;
                        case NacosConstants.KEY_DATA_ID:
                        case NacosConstants.KEY_NAME:
                            config.setDataId(v);
                            break;
                        case NacosConstants.KEY_GROUP:
                            config.setGroup(v);
                            break;
                        case NacosConstants.KEY_USERNAME:
                            config.setUsername(v);
                            break;
                        case NacosConstants.KEY_PASSWORD:
                            config.setPassword(v);
                            break;
                        case NacosConstants.KEY_LOADWAY:
                            config.setLoadWay(v);
                            break;
                        default:
                            break;
                    }
                });
            }
            PreConditionCheck.checkNotEmpty(config.getServerAddr(), "没有配置信息");
            // 目前仅支持http加载 后续考虑通过配置走 nacos  client 的方式 (基于http是考虑到并不是所有项目都是spring环境)
            INacosService service = NacosConstants.LOAD_HTTP.equals(config.getLoadWay())
                    ? NacosHttpServiceImpl.getInstance()
                    : NacosHttpServiceImpl.getInstance();
            String serviceConfig = service.getConfig(
                    config.getServerAddr(),
                    config.getNameSpace(),
                    config.getDataId(),
                    StringUtils.isEmpty(config.getGroup()) ? NacosConstants.DEFAULT_GROUP : config.getGroup(),
                    NacosConstants.DEFAULT_TIME_OUT
            );
            // 当前默认服务端都是properties
            Properties properties = new Properties();
            try (ByteArrayInputStream is = new ByteArrayInputStream(serviceConfig.getBytes(StandardCharsets.UTF_8))) {
                properties.load(is);
            }
            properties.forEach((k, v) -> {
                if (NacosConstants.NULL_STR.equalsIgnoreCase(String.valueOf(k))) {
                    return;
                }
                String value = String.valueOf(v);
                System.setProperty(String.valueOf(k), NacosConstants.NULL_STR.equalsIgnoreCase(value) ? "" : value);
            });
        } catch (Throwable e) {
            log.error("初始化配置异常", e);
        }
    }
}
