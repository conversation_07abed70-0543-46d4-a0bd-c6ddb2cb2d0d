package com.trs.spark.configuration.nacosconf;

import com.trs.common.utils.StringUtils;
import com.trs.spark.util.ClassUtils;
import com.trs.spark.util.ConfigUtils;
import com.trs.spark.util.YamlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import javax.annotation.Nullable;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/23 11:15
 * @version: 1.0
 */
public class NacosMetaSupplier {

    private static final Logger log = LoggerFactory.getLogger(NacosMetaSupplier.class);

    private static final String[] CONFIG_LOCATIONS = new String[]{"bootstrap.yml", "bootstrap.properties", "application.properties", "application.yml"};

    private static NacosMeta nacosMeta;

    /**
     * 读取spring工程的nacos配置项
     *
     * @return
     */
    public static NacosMeta get() {
        if (Objects.isNull(nacosMeta)) {
            initConfig();
        }
        return nacosMeta;
    }

    public static NacosMeta get(String resourcePath) {
        return getConfig(resourcePath);
    }

    private static void initConfig() {
        if (!Objects.isNull(nacosMeta)) {
            return;
        }
        nacosMeta = getConfig(CONFIG_LOCATIONS);
    }

    public static NacosMeta getConfig(String... resourcePath) {
        NacosMeta loadConf = new NacosMeta();
        NacosConstants.MetaInfo metaInfo = NacosConstants.MetaInfo.CONFIG;
        Map<String, String> confMap = new HashMap<>(0);
        // 再中配置文件中获取
        List<Properties> propertiesList = new ArrayList<>();
        // 填充一个空配置以便循环
        propertiesList.add(new Properties());
        for (String location : resourcePath) {
            propertiesList.addAll(loadPropertiesList(ClassUtils.getDefaultClassLoader(), location));
        }
        // 循环
        propertiesList.forEach(prop -> {
            addValue(metaInfo.getAddrPath(), prop, confMap);
            addValue(metaInfo.getExtensionPath(), prop, confMap);
            addValue(metaInfo.getNamespacePath(), prop, confMap);
            addValue(metaInfo.getNamePath(), prop, confMap);
            addValue(metaInfo.getGroupPath(), prop, confMap);
            addValue(metaInfo.getUsernamePath(), prop, confMap);
            addValue(metaInfo.getPasswordPath(), prop, confMap);
        });
        // 写回nacosConfig
        loadConf.setServerAddr(getStringOrEmpty(confMap.get(metaInfo.getAddrPath())));
        loadConf.setNameSpace(getStringOrEmpty(confMap.get(metaInfo.getNamespacePath())));
        loadConf.setDataId(getStringOrEmpty(confMap.get(metaInfo.getNamePath())));
        loadConf.setGroup(getStringOrEmpty(confMap.get(metaInfo.getGroupPath())));
        loadConf.setUsername(getStringOrEmpty(confMap.get(metaInfo.getUsernamePath())));
        loadConf.setPassword(getStringOrEmpty(confMap.get(metaInfo.getPasswordPath())));
        nacosMeta = loadConf;
        return loadConf;
    }

    private static void addValue(String path, Properties properties, Map<String, String> confMap) {
        // 从环境变量中获取配置
        String propertiesValue = ConfigUtils.getPropertiesValue(path);
        if (StringUtils.isNotEmpty(getStringOrEmpty(propertiesValue))) {
            confMap.put(path, getStringOrEmpty(propertiesValue));
            return;
        }
        // 尝试从properties中获取配置
        Object value = properties.get(path);
        if (StringUtils.isNotEmpty(confMap.get(path)) && !Objects.isNull(value)) {
            log.warn("配置{}已经存在值{}, 舍弃值{}", path, confMap.get(path), String.valueOf(value));
            return;
        }
        // 写入配置
        if (!Objects.isNull(value)) {
            confMap.put(path, getStringOrEmpty(String.valueOf(value)));
        }
    }

    private static String getStringOrEmpty(String s) {
        if (StringUtils.isEmpty(s) || NacosConstants.NULL_STR.equalsIgnoreCase(s)) {
            return "";
        }
        return s;
    }

    private static List<Properties> loadPropertiesList(@Nullable ClassLoader classLoader, String location) {
        List<Properties> result = new ArrayList<>();
        try {
            Enumeration<URL> urls = (classLoader != null ?
                    classLoader.getResources(location) :
                    ClassLoader.getSystemResources(location));
            while (urls.hasMoreElements()) {
                URL url = urls.nextElement();
                Properties prop = (location.endsWith(NacosConstants.YML) || location.endsWith(NacosConstants.YAML)) ? loadYamlProperties(url) : loadProperties(url);
                result.add(prop);
            }
            return result;
        } catch (IOException ex) {
            throw new IllegalArgumentException("Unable to load factories from location [" +
                    "{\"bootstrap.yml\", \"bootstrap.properties\", \"application.properties\", \"application.yml\"}" + "]", ex);
        }
    }

    private static Properties loadYamlProperties(URL url) throws IOException {
        Properties props = new Properties();
        InputStream is = getInputStream(url);
        try {
            Yaml yaml = new Yaml();
            props = YamlUtils.loadFromYaml(yaml.load(is));
        } finally {
            is.close();
        }
        return props;
    }

    private static Properties loadProperties(URL url) throws IOException {
        Properties props = new Properties();
        InputStream is = getInputStream(url);
        try {
            props.load(is);
        } finally {
            is.close();
        }
        return props;
    }

    private static InputStream getInputStream(URL url) throws IOException {
        URLConnection con = url.openConnection();
        try {
            return con.getInputStream();
        } catch (IOException ex) {
            // Close the HTTP connection (if applicable).
            if (con instanceof HttpURLConnection) {
                ((HttpURLConnection) con).disconnect();
            }
            throw ex;
        }
    }
}
