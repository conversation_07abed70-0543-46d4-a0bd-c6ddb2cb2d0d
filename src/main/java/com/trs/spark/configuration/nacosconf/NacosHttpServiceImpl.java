package com.trs.spark.configuration.nacosconf;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;
import com.dtflys.forest.config.ForestConfiguration;
import com.dtflys.forest.exceptions.ForestNetworkException;
import com.dtflys.forest.http.HttpStatus;
import com.trs.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Optional;

/**
 * @version 1.0
 * @description NacosHttpService <BR>
 * <p>
 * @author：zhang.wenquan
 * @create on 2021/9/7 16:10
 * @company TRS信息技术有限公司
 */
public class NacosHttpServiceImpl implements INacosService {

    private static final Logger log = LoggerFactory.getLogger(NacosHttpServiceImpl.class);

    /**
     * forest定义的接口
     */
    private Client client;

    /**
     * 本类的单例
     */
    private static NacosHttpServiceImpl httpService = new NacosHttpServiceImpl();

    /**
     * 服务器的授权token, 由于更新配置不需要很长的时间，所以不考虑检查token的存活状态
     */
    private String accessToken;

    private NacosHttpServiceImpl() {
        this.client = ForestConfiguration.configuration().createInstance(Client.class);
    }

    public static NacosHttpServiceImpl getInstance() {
        return httpService;
    }

    public String getAccessToken(String serverAddr) throws Exception {
        if (Boolean.TRUE.equals(this.needToken())) {
            if (StringUtils.isNullOrEmpty(accessToken)) {
                // needToken已经判断了nacosConfig不为空
                String username = Optional.ofNullable(NacosMetaSupplier.get().getUsername()).orElse("");
                String password = Optional.ofNullable(NacosMetaSupplier.get().getPassword()).orElse("");
                String result = client.getAccessToken(serverAddr, username, password);
                log.info("Nacos AccessToken response:{}", result);
                accessToken = JSONObject.parseObject(result).getString("accessToken");
            }
            if (StringUtils.isNullOrEmpty(accessToken)) {
                throw new Exception("未能获取到accessToken");
            }
            return accessToken;
        } else {
            return "";
        }
    }

    private boolean needToken() throws Exception {
        return StringUtils.isNotEmpty(NacosMetaSupplier.get().getUsername())
                || StringUtils.isNotEmpty(NacosMetaSupplier.get().getPassword());
    }

    @Override
    public boolean isHealth(String serverAddr) {
        try {
            String s = client.serverHealth(serverAddr, this.getAccessToken(serverAddr));
            String status = JSONObject.parseObject(s).getString("status");
            if (!NacosConstants.SERVER_IS_HEALTH.equals(status)) {
                return false;
            }
        } catch (Exception e) {
            log.error("获取服务器状态出错", e);
            return false;
        }
        return true;
    }

    @Override
    public String getConfig(String serverAddr, String nameSpace, String dataId, String group, long timeoutMs) throws Exception {
        timeoutMs = (Objects.isNull(timeoutMs) || timeoutMs <= 0L) ? NacosConstants.DEFAULT_TIME_OUT : timeoutMs;
        try {
            return client.getConfig(serverAddr, nameSpace, dataId, group, timeoutMs, this.getAccessToken(serverAddr));
        } catch (ForestNetworkException e) {
            if (HttpStatus.NOT_FOUND == e.getStatusCode()) {
                // 服务端不存在该项配置
                return "";
            } else {
                throw e;
            }
        }
    }

    @Override
    public boolean publishConfig(String serverAddr, String nameSpace, String dataId, String group, String content, String type) throws Exception {
        return client.publishConfig(serverAddr, nameSpace, dataId, group, content, type, this.getAccessToken(serverAddr));
    }

    interface Client {

        /**
         * Description: 向服务器获取验证
         *
         * @param serverAddr
         * @param username
         * @param password
         * @throws ForestNetworkException
         * @return: {
         * "accessToken":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTYwNTYyOTE2Nn0.2TogGhhr11_vLEjqKko1HJHUJEmsPuCxkur-CfNojDo",
         * "tokenTtl":18000,
         * "globalAdmin":true
         * }
         */
        @Post(url = "${serverAddr}/nacos/v1/auth/login", contentType = "multipart/form-data")
        String getAccessToken(
                @Var("serverAddr") String serverAddr,
                @Body("username") String username,
                @Body("password") String password) throws ForestNetworkException;

        /**
         * Description: 获取服务器状态
         *
         * @param serverAddr  服务器地址
         * @param accessToken token
         * @throws ForestNetworkException
         * @return: json
         */
        @Get(url = "${serverAddr}/nacos/v1/ns/operator/metrics?accessToken=${accessToken}")
        String serverHealth(@Var("serverAddr") String serverAddr, @Var("accessToken") String accessToken) throws ForestNetworkException;

        /**
         * Description: 获取服务端配置
         *
         * @param serverAddr  服务器地址
         * @param nameSpace   命名空间
         * @param dataId      配置名
         * @param group       组
         * @param timeoutMs   最长等待时间
         * @param accessToken token
         * @throws ForestNetworkException
         * @return: java.lang.String
         */
        @Get(url = "${serverAddr}/nacos/v1/cs/configs?dataId=${dataId}&group=${group}&tenant=${nameSpace}&accessToken=${accessToken}")
        String getConfig(
                @Var("serverAddr") String serverAddr,
                @Var("nameSpace") String nameSpace,
                @Var("dataId") String dataId,
                @Var("group") String group,
                @Var("timeoutMs") long timeoutMs,
                @Var("accessToken") String accessToken
        ) throws ForestNetworkException;

        /**
         * Description: 发布配置
         *
         * @param serverAddr  服务器地址
         * @param nameSpace   命名空间
         * @param dataId      配置文件名字
         * @param group       组
         * @param content     配置类容
         * @param type        配置类型 properties xml json text html yaml unset
         * @param accessToken token
         * @throws ForestNetworkException
         * @return: boolean
         */
        @Post(url = "${serverAddr}/nacos/v1/cs/configs",
                contentType = "multipart/form-data"
        )
        boolean publishConfig(
                @Var("serverAddr") String serverAddr,
                @Body("tenant") String nameSpace,
                @Body("dataId") String dataId,
                @Body("group") String group,
                @Body("content") String content,
                @Body("type") String type,
                @Body("accessToken") String accessToken
        ) throws ForestNetworkException;

    }
}
