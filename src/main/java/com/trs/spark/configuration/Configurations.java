package com.trs.spark.configuration;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.reflect.ClassUtils;
import com.trs.spark.exception.TrsSparkException;

import java.util.Map;
import java.util.Optional;

/**
 * 外部配置的门面，完成配置模式、配置文件加载实现的初始等工作
 *
 * <AUTHOR>
 */
public class Configurations {

    private Configurations() {
    }

    /**
     * 由于一个应用从外部配置文件加载的方式只有一种，因此我们可以将此loader初始后固化
     */
    private static IConfigurationLoader loader = null;

    public final static String SPARK_MASTER_PROPERTY = "spark.master";

    /**
     * 配置信息在文件里
     *
     * @param path 文件路径
     * @return
     */
    public static WhereIsConfig<String> inFile(String path) {
        return new ConfigInFile(PreConditionCheck.checkNotNull(path));
    }

    /**
     * 配置在Map里
     *
     * @param configs 具体配置信息
     * @return
     */
    public static WhereIsConfig<Map<String, Object>> inMap(Map<String, Object> configs) {
        return new ConfigInMap(configs);
    }

    /**
     * 获得配置加载器，加载器完成从外部读取配置到系统变量的操作
     *
     * @param whereIsConfig
     * @return
     * @throws TrsSparkException
     */
    public static IConfigurationLoader getLoader(WhereIsConfig<?> whereIsConfig) throws TrsSparkException {
        if (ClassUtils.isCompatible(whereIsConfig.getClass(), ConfigInMap.class)) {
            //构建一个从Map中加载的实现
            loader = new MapConfigurationLoader(((ConfigInMap) whereIsConfig).getConfig());
        } else if (ClassUtils.isCompatible(whereIsConfig.getClass(), ConfigInFile.class)) {
            //构建一个从配置文件加载的实现
            loader = new FileConfigurationLoader(((ConfigInFile) whereIsConfig).getConfig());
        } else if (ClassUtils.isCompatible(whereIsConfig.getClass(), ConfigInNacos.class)) {
            //构建一个从nacos加载的实现
            loader = new NacosConfigurationLoader((ConfigInNacos) whereIsConfig);
        } else {
            throw new TrsSparkException("目前只支持从Map或配置文件中读取外部配置信息");
        }
        return loader;
    }

    /**
     * 获得属性值
     *
     * @param propertyName
     * @return
     */
    public static Optional<String> getProperty(String propertyName) {
        return loader.getProperty(propertyName);
    }

}
