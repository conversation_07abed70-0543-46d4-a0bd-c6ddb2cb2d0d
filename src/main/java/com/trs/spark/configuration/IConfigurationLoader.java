package com.trs.spark.configuration;

import com.trs.common.utils.StringUtils;
import com.trs.spark.constant.RunMode;
import com.trs.spark.exception.TrsSparkException;

import java.util.Optional;

import static com.trs.spark.configuration.Configurations.SPARK_MASTER_PROPERTY;

/**
 * 加载用户自动配置进入系统变量的接口
 * 在Spark应用中，配置属性最好通过System.getProperties方式来获取，因此需要一个能够支持不同配置文件的加载并写入系统变量的工作
 *
 * <AUTHOR>
 */
public interface IConfigurationLoader {


    /**
     * 加到配置到系统变量当中，系统变量可能来自配置文件，也可能来自Map
     *
     * @param mode
     * @throws TrsSparkException
     */
    void loadIntoSystem(RunMode mode) throws TrsSparkException;

    /**
     * 获取配置项
     *
     * @param propertyName 配置项名
     * @return 结果
     */
    default Optional<String> getProperty(String propertyName) {
        if (StringUtils.isNullOrEmpty(propertyName)) {
            return Optional.empty();
        } else {
            return Optional.ofNullable(System.getProperty(propertyName, null));
        }
    }

    /**
     * 校验有效性
     *
     * @throws TrsSparkException 异常
     */
    default void validate() throws TrsSparkException {
        if (!Optional.ofNullable(System.getProperty(SPARK_MASTER_PROPERTY, null)).isPresent()) {
            throw new TrsSparkException(String.format("必须在配置文件或传入的Map指定%s属性", SPARK_MASTER_PROPERTY));
        }
    }

}
