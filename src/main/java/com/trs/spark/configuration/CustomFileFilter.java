package com.trs.spark.configuration;

import java.io.File;
import java.io.FileFilter;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class CustomFileFilter implements FileFilter {

    private Function<File, Boolean> judger;

    public CustomFileFilter(Function<File, Boolean> judger) {
        this.judger = judger;
    }

    /**
     * Tests if a specified file should be included in a file list.
     *
     * @param dir the directory in which the file was found.
     * @return <code>true</code> if and only if the name should be
     * included in the file list; <code>false</code> otherwise.
     */
    @Override
    public boolean accept(File dir) {
        return judger.apply(dir);
    }
}
