package com.trs.spark.configuration;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.io.FileUtils;
import com.trs.common.utils.StringUtils;
import com.trs.spark.constant.RunMode;
import com.trs.spark.exception.TrsSparkException;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * 加载配置文件到System当中。该类会根据运行模式（RunMode）获得配置根路径，并根据传入的配置文件相对地址找到读取配置文件。
 * 例如：
 * 对于部署模式RunMode.DEPLOY，spark作业应用为MySparkPi.jar，程序会定位到MySparkPi.jar所在路径，并根据传入的propertiesFilePath
 * 找到配置文件，并进行加载。
 * 注意：如果我们有多个配置文件，propertiesFilePath只需要配置到最上层文件夹，程序会自动寻找到其下所有的properties文件
 *
 * <AUTHOR>
 */
public class FileConfigurationLoader implements IConfigurationLoader {

    /**
     * 配置文件相对路径
     */
    private String propertiesFilePath;

    public static final String PROPERTIES = "properties";

    public FileConfigurationLoader(String path) {
        this.propertiesFilePath = PreConditionCheck.checkNotNull(path);
    }

    private BiFunction<Boolean, String, List<String>> findPropertiesPathFunction =
            (isProperties, filePath) -> findPropertiesPath(isProperties, filePath);


    /**
     * 加到配置到系统变量当中，系统变量可能来自配置文件，也可能来自Map
     *
     * @param mode
     * @throws TrsSparkException
     */
    @Override
    public void loadIntoSystem(RunMode mode) throws TrsSparkException {
        //获得作业的配置文件的根路径
        String rootPath = getRootPath(mode);
        //根据根路径扫描找到所有的配置文件
        List<String> propertiesPath = getPropertyFilePaths(
                rootPath,
                Optional.ofNullable(propertiesFilePath),
                findPropertiesPathFunction);
        //逐个遍历properties文件直到将所有环境变量加载到System当中
        for (String propFile : propertiesPath) {
            try {
                InputStream in = FileUtils.asByteSource(new File(propFile)).openStream();
                Properties property = new Properties();
                property.load(in);
                for (Map.Entry<Object, Object> keyAndValue : property.entrySet()) {
                    System.setProperty(String.valueOf(keyAndValue.getKey()), String.valueOf(keyAndValue.getValue()));
                }
            } catch (IOException ioex) {
                throw new TrsSparkException(String.format("从%s读取到配置文件失败！", propFile), ioex);
            }
        }
        validate();
    }

    /**
     * 找到配置文件
     *
     * @param isProperties
     * @param filePath
     * @return 结果
     */
    private List<String> findPropertiesPath(Boolean isProperties, String filePath) {
        List<String> propertiesPath = new ArrayList<>();
        if (isProperties) {
            propertiesPath.add(filePath);
        } else {
            File configRootPath = new File(filePath);
            //得到当前路径的配置文件
            List<String> rootProperties = Arrays.stream(configRootPath.listFiles(
                            new CustomFileFilter(file -> file.isFile() && PROPERTIES.equalsIgnoreCase(FileUtils.getPrefixName(file)))))
                    .map(File::getPath).collect(Collectors.toList());
            List<String> subpackagesProperties = Arrays.stream(configRootPath.listFiles(new CustomFileFilter(file -> file.isDirectory())))
                    .flatMap(child -> findPropertiesPath(false, child.getPath()).stream())
                    .collect(Collectors.toList());
            propertiesPath.addAll(rootProperties);
            propertiesPath.addAll(subpackagesProperties);
        }
        return propertiesPath;
    }

    /**
     * 获得根路径
     *
     * @param mode 模式
     * @return 结果
     */
    private String getRootPath(RunMode mode) {
        //format is file:/$APPLICATION_JAR/SparkProgramForMLF-1.0-SNAPSHOT.jar!
        String jarPath = FileConfigurationLoader.class.getResource("").getPath();
        String jarPosition = jarPath;
        switch (mode) {
            case DEPLOY: {
                jarPosition = jarPath.substring(0, jarPath.lastIndexOf("!"));
                //$APPLICATION_JAR
                jarPosition.substring(jarPosition.indexOf("/"), jarPosition.lastIndexOf("/"));
                break;
            }
            default:
                break;
        }
        return jarPosition;
    }

    /**
     * 根据根路径和配置文件路径找到所有的properties文件
     *
     * @param rootPath      rootPath，当configPath不是绝对路径时，会使用
     * @param optConfigPath 配置文件路径，可是绝对地址，也可以是相对地址，也可以指向properties文件，也可以指向文件夹
     * @param findPaths     提取得到最终的想要的配置文件，这里这么写是将提取文件的逻辑由调用者自己决定
     *                      Boolean：传入路径是否是properties文件夹
     *                      String：传入路径
     *                      List<String>: 传入路径下所有的properties文件的绝对路径
     * @return
     */
    private List<String> getPropertyFilePaths(String rootPath,
                                              Optional<String> optConfigPath,
                                              BiFunction<Boolean, String, List<String>> findPaths) throws TrsSparkException {
        if (StringUtils.isNullOrEmpty(rootPath) || !optConfigPath.isPresent()) {
            throw new TrsSparkException("无法加载配置文件，因为rootPath或configPath是空的");
        }
        String configPath = optConfigPath.get();
        File configFile = new File(configPath);
        configFile = configFile.exists() ? configFile : new File(String.format("%s/%s", rootPath, configPath));
        if (configFile.exists()) {
            //configPath是一个绝对路径
            if (FileUtils.TYPE._DIRECTORY.isMatch(configFile)) {
                return findPaths.apply(false, configFile.getPath());
            } else if (FileUtils.TYPE._FILE.isMatch(configFile) && PROPERTIES.equalsIgnoreCase(FileUtils.getPrefixName(configFile))) {
                return findPaths.apply(true, configFile.getPath());
            } else {
                throw new TrsSparkException(String.format("%s应该是一个文件夹或是一个properties文件"));
            }
        } else {
            throw new TrsSparkException(String.format("%s不存在", configFile.getPath()));
        }
    }

}
