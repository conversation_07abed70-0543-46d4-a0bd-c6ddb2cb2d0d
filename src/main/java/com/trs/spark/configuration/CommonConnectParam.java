package com.trs.spark.configuration;

import com.trs.common.context.CommonContext;
import com.trs.spark.constant.ConfigureConstant;
import lombok.Data;

import java.util.Optional;

/**
 * 创建连接是的参数属性
 * *@author: lu.ziyong
 * *@create 2021-04-08 11:28
 **/
@Data
public class CommonConnectParam extends CommonContext {

    /**
     * 连接超时时间 单位毫秒
     */
    private Long connectTimeOut;

    public boolean isHttpsProtocol() {
        return "https".equalsIgnoreCase(getProperty(ConfigureConstant.DEFAULT_CONNECTION_PROTOCOL_KEY,"https"));
    }

    public boolean containCertificate() {
        return Optional.ofNullable(getProperty(ConfigureConstant.DEFAULT_HTTPS_SSL_CONTENT_KEY,null)).isPresent();
    }
}
