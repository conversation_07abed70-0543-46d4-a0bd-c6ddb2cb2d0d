package com.trs.spark.configuration;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/23 17:45
 * @version: 1.0
 */
public class ConfigInNacos implements WhereIsConfig<String> {

    private final String resourcePath;

    private final Map<String, String> map;

    public ConfigInNacos(String resourcePath) {
        this(resourcePath, Collections.emptyMap());
    }

    public ConfigInNacos(String resourcePath, Map<String, String> map) {
        this.resourcePath = resourcePath;
        this.map = Objects.isNull(map) ? Collections.emptyMap() : map;
    }

    public Map<String, String> getMap() {
        return map;
    }

    @Override
    public String getConfig() {
        return resourcePath;
    }
}
