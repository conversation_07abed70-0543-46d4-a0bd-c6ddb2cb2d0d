package com.trs.spark.configuration;

import com.trs.spark.configuration.nacosconf.NacosConfigInitializer;
import com.trs.spark.constant.RunMode;
import com.trs.spark.exception.TrsSparkException;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/23 17:47
 * @version: 1.0
 */
public class NacosConfigurationLoader implements IConfigurationLoader {

    private final ConfigInNacos config;

    public NacosConfigurationLoader(ConfigInNacos config) {
        this.config = config;
    }

    @Override
    public void loadIntoSystem(RunMode mode) throws TrsSparkException {
        NacosConfigInitializer.initConf(config.getConfig(), config.getMap());
    }
}
