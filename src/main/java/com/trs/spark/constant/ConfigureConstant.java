package com.trs.spark.constant;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2022</p>
 * <p>Company:      www.trs.com.cn</p>
 * 相关配置常量
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL><br/>
 * <p>
 * 创建时间：2022/4/2 16:11
 * @version 1.0
 * @since 1.0
 */
public class ConfigureConstant {

    /**
     * DB-SDK默认使用的数据库<br/>
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_KEY = "trs.db.default.repository";
    /**
     * DB-SDK默认使用的数据库<br/>
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_TYPE_KEY = "trs.db.default.repository.type";
    /**
     * DB-SDK默认数据库的主机<br/>
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_HOST_KEY = "trs.db.default.repository.host";
    /**
     * DB-SDK默认数据库的端口<br/>
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_PORT_KEY = "trs.db.default.repository.port";
    /**
     * DB-SDK默认数据库的用户<br/>
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_USER_KEY = "trs.db.default.repository.user";
    /**
     * DB-SDK默认数据库的密码<br/>
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_PASSWORD_KEY = "trs.db.default.repository.password";

    /**
     * DB-SDK默认数据库名（目前MySQL需要）<br/>
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_DBNAME_KEY = "trs.db.default.repository.dbName";

    /**
     * DB-SDK是否开启kerberos认证
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_KERBEROS_KEY = "trs.db.default.repository.enableKerberos";

    /**
     * DB-SDK开启kerberos认证后的云类型
     */
    public static final String DEFAULT_REPOSITORY_CONFIG_CLOUD_TYPE_KEY = "trs.db.default.repository.cloudType";
    public static final String DEFAULT_REPOSITORY_CONFIG_USERKERTABPATH_KEY = "trs.db.default.repository.userKerTabPath";
    public static final String DEFAULT_REPOSITORY_CONFIG_KRB5CONFPATH_KEY = "trs.db.default.repository.krb5ConfPath";
    public static final String DEFAULT_REPOSITORY_CONFIG_PRINCIPAL_KEY = "trs.db.default.repository.principal";
    public static final String DEFAULT_REPOSITORY_CONFIG_ZOOKEEPER_PRINCIPAL_KEY = "trs.db.default.repository.zookeeperPrincipal";

    /**
     * 网络协议：http/https
     */
    public static final String DEFAULT_CONNECTION_PROTOCOL_KEY = "trs.db.default.connection.protocol";
    /**
     * SSL证书路径。
     * 如果网络协议是HTTPS，允许使用SSL证书替代用户名密码，配置项可以是一个程序可访问的路径，也可以是完整的密匙内容。
     * 注意：如果使用ssl方式，trs.db.default.repository.password的值为证书密码
     * 即将被废弃，新的请使用{@link ConfigureConstant#DEFAULT_HTTPS_SSL_CONTENT_KEY}
     */
    @Deprecated
    public static final String DEFAULT_HTTPS_SSL_CONTENT_KEY_OLD = "trs.db.https.ssl.content";
    public static final String DEFAULT_HTTPS_SSL_CONTENT_KEY = "trs.db.default.https.ssl.content";
    /**
     * 海贝链接中HTTP的检测周期（毫秒）<br/>
     */
    public static final String HYBASE_REPOSITORY_CONFIG_CONNECT_CHECK_MONITOR_INTERVAL = "trs.db.hybase.repository.connect.check.monitor.interval";
    /**
     * 海贝链接中HTTP的闲置时间（毫秒）<br/>
     */
    public static final String HYBASE_REPOSITORY_CONFIG_CONNECT_CHECK_IDLE_ALIVE = "trs.db.hybase.repository.connect.check.idle.alive";
    /**
     * 使用带HTTP检测的海贝链接类（true/false）<br/>
     */
    public static final String HYBASE_REPOSITORY_CONFIG_CONNECT_POOLING_WITH_CHECK = "trs.db.hybase.repository.config.connect.pooling.with.check";
    /**
     * 使用非连接池的海贝链接类（true/false）<br/>
     */
    public static final String HYBASE_REPOSITORY_CONFIG_CONNECT_NOPOOLING = "trs.db.hybase.repository.config.connect.nopooling";

    /**
     * 使用非连接池的ES链接类（true/false）<br/>
     */
    public static final String ES_REPOSITORY_CONFIG_CONNECT_NOPOOLING = "trs.db.es.repository.config.connect.nopooling";
    /**
     * hybase高亮字段检索切分大小<br/>
     */
    public static final String HYBASE_REPOSITORY_CONFIG_HIGHLIGHT_CUTSIZE = "trs.db.hybase.repository.config.highlight.cutsize";

    /**
     * ES默认的客户端连接类型
     */
    public static final String ES_REPOSITORY_CONNECTION = "trs.db.es.default.connection.client";
    public static final String ES_REPOSITORY_CONNECTION_CLIENT_TYPE = "trs.db.es.default.connection.client.type";
    public static final String ES_REPOSITORY_CONNECTION_HTTP_CLIENT_CONFIG_CALLBACK = "trs.db.es.default.connection.http.client.config.callback";

    /**
     * 当ES设置正序排列时，当某个字段没有值时，默认是_last模式，即放在最后面，这里控制是否需要放在最前面
     */
    public static final String ES_NEED_ASC_FIRST_ON_MISSING = "trs.db.es.default.need.missing.asc.first";

    /**
     * ES标记是否是嵌套对象的分组统计
     */
    public static final String ES_NESTED_OBJECT_CATEGORY_QUERY = "es_nested_object_category_query";

    /**
     * 是嵌套对象
     */
    public static final String IS_ES_NESTED_OBJECT = "is_es_nested_object";

    /**
     * 是否启用kerberos认证
     */
    public static final String KERBEROS_ENABLE = "kerberos_enable";

    public static final String USER_KEYTAB_PATH = "userKeytabPath";

    public static final String KRB5_PATH = "krb5Path";

    /**
     * principal
     */
    public static final String PRINCIPAL = "principal";

    public static final String SERVICE_DISCOVER_MODE = "serviceDiscoveryMode";

    public static final String ZOOKEEPER_NAMESPACE = "zooKeeperNamespace";

    /**
     * sasl_qop
     */
    public static final String SASL_QOP = "sasl_qop";

    /**
     * ES中自定义QueryBuilder
     */
    public static final String ES_CUSTOM_QUERYBUILDER_FUNCTION = "ES_Custom_QueryBuilder_Function";

    public static final String CLOUD_TYPE_HUAWEI = "huawei";
    public static final String CLOUD_TYPE_H3C = "h3c";
}
