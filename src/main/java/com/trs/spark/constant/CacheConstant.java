package com.trs.spark.constant;


/**
 * 缓存的相关常量
 *
 * <AUTHOR>
 */
public class CacheConstant {

    /**
     * 是否启用缓存
     */
    public static final String CACHE_ENABLE = "media.base.spark.cache.enable";

    /**
     * 缓存数据源连接地址
     */
    public static final String CACHE_URL = "media.base.spark.cache.url";

    /**
     * 缓存数据源账号
     */
    public static final String CACHE_USERNAME = "media.base.spark.cache.username";

    /**
     * 缓存数据源密码
     */
    public static final String CACHE_PASSWORD = "media.base.spark.cache.password";

    /**
     * 通过hdfs缓存的路径前缀
     */
    public static final String CACHE_HDFS_PREFIX = "media.base.spark.cache.key.prefix";

    /**
     * 通过hdfs缓存的默认路径
     */
    public static final String CACHE_HDFS_DEFAULT_PREFIX = "/user/trs/cache";
}
