package com.trs.spark.constant;

/**
 * <AUTHOR>
 */
public interface RedisConstants {

    String PKG = "org.apache.spark.sql.redis";


    /**
     * 设置redis地址的配置
     */
    String PROPERTIES_HOST = "spark.redis.host";

    /**
     * 设置redis端口的配置
     */
    String PROPERTIES_PORT = "spark.redis.port";

    /**
     * 设置redis dbnum配置
     */
    String PROPERTIES_DB = "spark.redis.db";

    /* option(https://github.com/RedisLabs/spark-redis/blob/master/doc/dataframe.md) */

    /**
     * 存储在redis中的方式<br>
     * <li>binary 以二进制存储</li>
     * <li>hash 以hash方式存储 默认方式</li>
     */
    String OPTION_MODEL = "model";

    /**
     * 主键<br>
     * 未指定情况下以hash存储<br>
     * 指定了主键的情况下以主键存储<br>
     * 比如person数据 idCard:511 name:张三 age:25<br>
     * 指定key.column=idCard 则redis生成的key为：person:511<br>
     * 未指定的情况下 key为：person:0c56d5ae080e4289929af78758efe777
     */
    String OPTION_KEY_COLUMN = "key.column";

    /**
     * ttl
     */
    String OPTION_TTL = "ttl";

    /**
     * 地址
     */
    String OPTION_HOST = "host";

    /**
     * 端口
     */
    String OPTION_PORT = "port";

    /**
     * 账号
     */
    String OPTION_USER = "user";

    /**
     * 密码
     */
    String OPTION_AUTH = "auth";

    /**
     * db号
     */
    String OPTION_DB = "dbNum";

    /**
     * 连接超时时间
     */
    String OPTION_TIMEOUT = "timeout";

    /**
     * redis前缀
     */
    String OPTION_TABLE = "table";
}
