package com.trs.spark.parser;

import com.trs.common.datasource.parser.VirtualUrlParser;
import com.trs.common.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/8/15
 */
public class EsHlcParser extends VirtualUrlParser {

    @Override
    public String getDataBaseName() {
        return "esHlc";
    }

    /**
     * 得到给行的url所包含的port信息,实现原理与host类似
     *
     * @return
     * @throws NullPointerException 如果没有找到url,将抛出空指针异常
     */
    @Override
    public String port() throws NullPointerException {
        return getPairsOfIPAndPort().values()
                .stream()
                .filter(StringUtils::isNotEmpty)
                .findAny()
                .orElse("");
    }
}
