package com.trs.spark.util;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.RowFactory;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import scala.Tuple2;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Dataset转换工具类
 * *@author:wen.wen
 * *@create 2023-07-12 23:36
 **/
public class DatasetConvertUtils {

    /**
     * 将一个List<Map> 转换成DataSet
     *
     * @param sparkSession SparkSession
     * @param records      记录
     */
    public static Dataset<Row> javaMap2Dataset(SparkSession sparkSession, List<Map<String, Object>> records) {
        Tuple2<List<Row>, StructType> type = rowAndStructType(records);
        return sparkSession.createDataFrame(type._1(), type._2);
    }

    /**
     * 将List<Map<String, Object>>转换成List<Row>，并得到它的StructType
     *
     * @param records 待转换的记录
     * @return Tuple2 Tuple2._1:List<Row>，Tuple2._2:StructType
     */
    public static Tuple2<List<Row>, StructType> rowAndStructType(List<Map<String, Object>> records) {
        Map<String, Object> tempMap = new LinkedHashMap<>(records.size());
        //声明一个临时map,存储整个List<Map> 中所有的key和其对应的至少一个value,后面会根据此来构建
        records.forEach((data) -> data.forEach(tempMap::putIfAbsent));
        //根据tempMap，构造StructType
        StructType schema = DataTypes.createStructType(tempMap.keySet()
                .stream()
                .map(key -> DataTypes.createStructField(key, DataTypeUtils.getDataType(tempMap.get(key)), true))
                .toArray(StructField[]::new));
        //需要注意的是,StructType中的类型的顺序必须和Row的值的顺序对应起来,否则创建不了dataset
        //将records按照tempMap中的key的顺序,构造Row
        List<Row> rowList = records.stream()
                .map(data ->
                        tempMap.keySet()
                                .stream()
                                .map(data::get)
                                .collect(Collectors.toList())
                )
                .map(values -> RowFactory.create(values.toArray()))
                .collect(Collectors.toList());
        return Tuple2.apply(rowList, schema);
    }
}
