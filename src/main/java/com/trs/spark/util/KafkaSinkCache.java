package com.trs.spark.util;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import org.apache.kafka.clients.producer.KafkaProducer;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/4/27 10:46
 * @version 1.0
 * @since 1.0
 */
public class KafkaSinkCache {

    /**
     * 将Map<String,Object>转换成Seq<(String,Object)>，
     * 这里没有转换成string或者其他类型主要原因是在removeListener中，
     * 后期有可能会用到这个seq来进行其他的行为
     * 将 Map<String, Object> 转换为 Seq<(String, Object)>
     * 这里没有转换为 String 或其他类型是因为在 removeListener 中，
     * 以后可能会使用该 Seq 进行其他操作。
     */
    private static class ProducerConf {
        private final Iterable<Map.Entry<String, Object>> entries;

        public ProducerConf(Map<String, Object> map) {
            this.entries = map.entrySet();
        }

        public Iterable<Map.Entry<String, Object>> getEntries() {
            return entries;
        }
    }

    /**
     * 新建 RemovalListener 并在回调中关闭 Producer
     */
    private static final RemovalListener<ProducerConf, KafkaProducer<?, ?>> REMOVAL_LISTENER =
            new RemovalListener<ProducerConf, KafkaProducer<?, ?>>() {
                @Override
                public void onRemoval(RemovalNotification<ProducerConf, KafkaProducer<?, ?>> notification) {
                    notification.getValue().close();
                }
            };

    /**
     * 缓存添加后20分钟后关闭
     */
    private static final Cache<ProducerConf, KafkaProducer<?, ?>> CACHE =
            CacheBuilder.newBuilder()
                    .maximumSize(10000)
                    .expireAfterWrite(20, TimeUnit.MINUTES)
                    .removalListener(REMOVAL_LISTENER)
                    .build();

    public static <K, V> KafkaProducer<K, V> getProducer(Map<String, Object> config) throws ExecutionException {
        ProducerConf producerConf = new ProducerConf(config);
        return (KafkaProducer<K, V>) CACHE.get(producerConf, new Callable<KafkaProducer<?, ?>>() {
            @Override
            public KafkaProducer<?, ?> call() {
                return new KafkaProducer<>(config);
            }
        });
    }
}
