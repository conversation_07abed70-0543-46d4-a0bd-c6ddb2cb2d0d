package com.trs.spark.util;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/23 15:09
 * @version: 1.0
 */
public class YamlUtils {

    public static <T> Properties loadFromYaml(T yamlLoad) {
        Properties properties = new Properties();
        try {
            List<String> strings = travelRootWithResult(yamlLoad);
            String content = strings.stream().collect(Collectors.joining("\n"));
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8))) {
                properties.load(inputStream);
            }
        } catch (Throwable e) {

        }
        return properties;
    }

    private static List<String> travelRootWithResult(Object object) {
        List<String> resultList = new ArrayList<>();
        if (object instanceof LinkedHashMap) {
            LinkedHashMap map = (LinkedHashMap) object;
            Set<Object> keySet = map.keySet();
            for (Object key : keySet) {
                List<String> keyList = new ArrayList<>();
                keyList.add((String) key);
                travelTreeNode(map.get(key), keyList, resultList);
            }
        }
        return resultList;
    }


    private static void travelTreeNode(Object obj, List<String> keyList, List<String> resultList) {
        if (obj instanceof LinkedHashMap) {
            LinkedHashMap linkedHashMap = (LinkedHashMap) obj;
            linkedHashMap.forEach((key, value) -> {
                if (value instanceof LinkedHashMap) {
                    keyList.add((String) key);
                    travelTreeNode(value, keyList, resultList);
                    keyList.remove(keyList.size() - 1);
                } else {
                    StringBuilder result = new StringBuilder();
                    for (String strKey : keyList) {
                        result.append(strKey).append(".");
                    }
                    result.append(key).append("=").append(value);
                    resultList.add(result.toString());
                }
            });
        } else {
            StringBuilder result = new StringBuilder();
            result.append(keyList.get(0)).append("=").append(obj);
            resultList.add(result.toString());
        }
    }
}
