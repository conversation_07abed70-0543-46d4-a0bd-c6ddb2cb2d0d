package com.trs.spark.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * DataType工具类
 * *@author:wen.wen
 * *@create 2023-07-12 23:34
 **/
public class DataTypeUtils implements Serializable {

    /**
     * 根据值的class类型获取其对应的DataType
     *
     * @param value 值
     */
    public static DataType getDataType(Object value) {
        //如果value为空，则默认返回NullType
        if (value == null) {
            return DataTypes.NullType;
        }
        Class<?> clz = value.getClass();
        if (String.class.isAssignableFrom(clz)) {
            return DataTypes.StringType;
        }
        if (Integer.class.isAssignableFrom(clz) || int.class.isAssignableFrom(clz)) {
            return DataTypes.IntegerType;
        }
        if (Long.class.isAssignableFrom(clz) || long.class.isAssignableFrom(clz)) {
            return DataTypes.LongType;
        }
        if (Double.class.isAssignableFrom(clz) || double.class.isAssignableFrom(clz)) {
            return DataTypes.FloatType;
        }
        if (Float.class.isAssignableFrom(clz) || float.class.isAssignableFrom(clz)) {
            return DataTypes.FloatType;
        }
        if (Date.class.isAssignableFrom(clz)) {
            return DataTypes.DateType;
        }
        if (String[].class.isAssignableFrom(clz)) {
            return DataTypes.createArrayType(DataTypes.ShortType, true);
        }
        if (Integer[].class.isAssignableFrom(clz) || int[].class.isAssignableFrom(clz)) {
            return DataTypes.createArrayType(DataTypes.IntegerType, true);
        }
        //一般来讲,json格式的都继承自Map接口，如果有其他类型的，后续再扩充
        if (JSONObject.class.isAssignableFrom(clz) || Map.class.isAssignableFrom(clz)) {
            return getJsonStructType(value);
        }
        //一般来讲,集合格式的都继承自List接口，如果有其他类型的，后续再扩充
        if (List.class.isAssignableFrom(clz)) {
            return getListStructType((List) value);
        }
        if (Short.class.isAssignableFrom(clz) || short.class.isAssignableFrom(clz)) {
            return DataTypes.ShortType;
        }
        if (Long[].class.isAssignableFrom(clz) || long[].class.isAssignableFrom(clz)) {
            return DataTypes.createArrayType(DataTypes.LongType, true);
        }
        if (Double[].class.isAssignableFrom(clz) || double[].class.isAssignableFrom(clz)) {
            return DataTypes.createArrayType(DataTypes.DoubleType, true);
        }
        if (Float[].class.isAssignableFrom(clz) || float[].class.isAssignableFrom(clz)) {
            return DataTypes.createArrayType(DataTypes.FloatType, true);
        }
        if (Short[].class.isAssignableFrom(clz) || short[].class.isAssignableFrom(clz)) {
            return DataTypes.createArrayType(DataTypes.ShortType, true);
        }
        if (Byte[].class.isAssignableFrom(clz) || byte[].class.isAssignableFrom(clz)) {
            return DataTypes.createArrayType(DataTypes.ByteType, true);
        }
        if (Byte.class.isAssignableFrom(clz) || byte.class.isAssignableFrom(clz)) {
            return DataTypes.ByteType;
        }
        if (Boolean.class.isAssignableFrom(clz) || boolean.class.isAssignableFrom(clz)) {
            return DataTypes.BooleanType;
        }

        throw new RuntimeException(String.format("value=%s未找到spark中其对应的类型!", value));
    }

    /**
     * 获取值的类型为json的DataType
     *
     * @param value 值
     */
    private static DataType getJsonStructType(Object value) {
        if (Map.class.isAssignableFrom(value.getClass())) {
            Map<String, Object> tempMap = (Map<String, Object>) value;
            StructType jsonSchema = DataTypes.createStructType(
                    tempMap
                            .keySet()
                            .stream()
                            .map(key -> DataTypes.createStructField(key, getDataType(tempMap.get(key)), true))
                            .toArray(StructField[]::new));
            return DataTypes.createMapType(DataTypes.StringType, jsonSchema, true);
        }
        return DataTypes.NullType;
    }

    /**
     * 获取数组类型的DataType
     *
     * @param values 值
     */
    private static DataType getListStructType(List values) {
        Object value = values.get(0);
        DataType dataType = getDataType(value);
        return DataTypes.createArrayType(dataType, true);
    }
}
