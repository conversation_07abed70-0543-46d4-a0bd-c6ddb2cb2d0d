package com.trs.spark.util;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.datasource.BaseSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Column;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.ServiceLoader;

/**
 * <AUTHOR>
 */
@Slf4j
public class SourceUtils {

    public static final Integer NUMBER_2 = 2;
    public static final Integer NUMBER_3 = 3;
    public static final String STRING_AS = "as";

    private static final List<BaseSource> SOURCES = new ArrayList<>();

    static {
        log.info("开始通过SPI加载BaseSource.class");
        for (BaseSource source : ServiceLoader.load(BaseSource.class)) {
            log.info("加载了[{}]", source.getClass().getName());
            SOURCES.add(source);
        }
    }

    public static BaseSource makeSource(SourceDriver sourceDriver) {
        SourceUrlParser urlParser = sourceDriver.getParser();
        for (BaseSource source : SOURCES) {
            if (source.supportSourceUrlParser(urlParser)) {
                return source;
            }
        }
        throw new IllegalArgumentException("无法解析对应的source");
    }

    public static Column[] makeColumns(String selectFields) {
        if (StringUtils.isNotEmpty(selectFields)
                && !StringUtils.STRING_MULTIPLY_FLAG.equals(selectFields)) {
            return Arrays.stream(selectFields.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                    .filter(StringUtils::isNotEmpty)
                    .map(String::trim)
                    .filter(StringUtils::isNotEmpty)
                    .map(field -> {
                        String[] s = Arrays.stream(field.split(StringUtils.SEPARATOR_BLANK_SPACE))
                                .filter(StringUtils::isNotEmpty)
                                .toArray(String[]::new);
                        if (s.length == NUMBER_2) {
                            return new Column(s[0]).alias(s[1]);
                        } else if (s.length == NUMBER_3 && STRING_AS.equalsIgnoreCase(s[1])) {
                            return new Column(s[0]).alias(s[2]);
                        } else {
                            return new Column(field);
                        }
                    }).toArray(Column[]::new);
        }
        return new Column[0];
    }
}
