package com.trs.spark.util;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.utils.StringUtils;
import com.trs.hybase.client.*;
import com.trs.hybase.client.params.ConnectParams;
import com.trs.hybase.client.params.OperationParams;
import com.trs.hybase.hadoop.common.HybaseSparkUtils;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.function.Function;
import io.vavr.control.Either;
import io.vavr.control.Try;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.types.StructType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Optional;

import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * 海贝操作相关工具类
 * *@author:wen.wen
 * *@create 2023-03-17 14:08
 **/
public class HybaseUtils implements Serializable {


    /**
     * 保存数据到海贝
     *
     * @param sourceDriver SourceDriver
     * @param tableName    表名
     * @param dataset      数据集
     */
    public static <D> Either<TrsSparkException, Boolean> save(SourceDriver sourceDriver, String tableName, Dataset<D> dataset) {
        Either<Throwable, TRSConnection> tryTrsConnection = getConnectionFunctionV2(sourceDriver);
        try {
            TRSReport loadReport = new TRSReport();
            OperationParams loadOptions = new OperationParams();
            loadOptions.setProperty("insert.skip.error", "true");
            loadOptions.setProperty("insert.duplicate.error", "false");
            Function<Row, TRSRecord> toTrsRecord = HybaseSparkUtils::row2Record;
            dataset.toDF().javaRDD().foreachPartition((itr) -> {
                //将row转换成TRSInputRecord
                ArrayList<TRSInputRecord> buffers = new ArrayList<>();
                if (tryTrsConnection.isLeft()) {
                    throw new TrsSparkException("没有办法构建hybase的连接 !", tryTrsConnection.getLeft());
                }
                TRSConnection trsConnection = tryTrsConnection.get();
                //执行插入，以5000条为一个批次进行插入
                while (itr.hasNext()) {
                    Row row = itr.next();
                    TRSInputRecord trsInputRecord = new TRSInputRecord(toTrsRecord.apply(row));
                    buffers.add(trsInputRecord);
                    if (buffers.size() >= 5000) {
                        //达到5000保存数据库
                        ArrayList<TRSInputRecord> finalBuffers = buffers;
                        Try.run(() -> trsConnection.executeInsert(tableName, finalBuffers, loadOptions, loadReport)).get();
                        //插入完成后，清空buffers
                        buffers = new ArrayList<>();
                    }
                }

                //最终还有剩余不足5000条的数据
                ArrayList<TRSInputRecord> finalBuffers1 = buffers;
                Try.run(() -> trsConnection.executeInsert(tableName, finalBuffers1, loadOptions, loadReport)).get();
            });

            return Either.right(true);
        } catch (Exception e) {
            return Either.left(new TrsSparkException(String.format("执行海贝数据插入发生异常,异常信息为:%s", e.getMessage()), e));
        }
    }

    public static Either<Throwable, TRSConnection> getConnectionFunctionV2(SourceDriver sourceDriver) {
        return Either.right(getConnectionFunction(sourceDriver));
    }

    /**
     * 获取海贝连接
     *
     * @param sourceDriver SourceDriver
     * @return 海贝连接
     */
    public static TRSConnection getConnectionFunction(SourceDriver sourceDriver) {
        String host = checkNotNull(sourceDriver.getHost());
        String port = checkNotNull(sourceDriver.getPort());
        String userName = checkNotNull(sourceDriver.getUserName());
        String password = checkNotNull(sourceDriver.getPassword());
        String serverList = String.format("http://%s:%s", host, port);
        return new TRSConnection(serverList, userName, password, new ConnectParams());
    }

    /**
     * 判断表是否存在
     *
     * @param tableName  表名
     * @param connection 连接
     */
    public static boolean databaseExists(String tableName, TRSConnection connection) throws TRSException {
        TRSDatabase[] databases = connection.getDatabases(tableName);
        return databases != null && databases.length > 0;
    }

    /**
     * 获取StructType
     *
     * @param sourceDriver SourceDriver
     * @param tableName    表名
     */
    public static StructType getStructType(SourceDriver sourceDriver, String tableName) throws TRSException {
        Optional<TRSDatabase> optionalTRSDatabase = getDatabase(sourceDriver, tableName);
        if (!optionalTRSDatabase.isPresent()) {
            throw new NullPointerException(tableName + "is not exists in Hybase !");
        }

        //构建结构类型
        return HybaseSparkUtils.buildSchema(optionalTRSDatabase.get(), StringUtils.STRING_MULTIPLY_FLAG);
    }

    /**
     * 获取海贝的TRSDatabase
     *
     * @param sourceDriver SourceDriver
     * @param tableName    表名
     * @throws TRSException
     */
    public static Optional<TRSDatabase> getDatabase(SourceDriver sourceDriver, String tableName) throws TRSException {
        TRSDatabase[] databases = getConnectionFunction(sourceDriver).getDatabases(tableName);
        if (databases == null || databases.length == 0) {
            return Optional.empty();
        }
        return Optional.of(databases[0]);
    }
}
