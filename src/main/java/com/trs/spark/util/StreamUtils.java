package com.trs.spark.util;

import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.function.CheckedFunction;
import io.vavr.control.Try;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.rdd.RDD;

import java.io.Serializable;
import java.util.Iterator;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * Stream流相关工具方法
 * *@author:wen.wen
 * *@create 2023-03-16 18:46
 **/
public class StreamUtils implements Serializable {

    /**
     * 将迭代器转换成Stream
     *
     * @param iterator 迭代器
     */
    public static <T> Stream<T> literators2Stream(Iterator<T> iterator) {
        Spliterator<T> spliterator = Spliterators.spliteratorUnknownSize(iterator, 0);
        return StreamSupport.stream(spliterator, false);
    }

    /**
     * 将迭代器转换成Stream
     *
     * @param iterator  迭代器
     * @param convertor 转换器
     */
    public static <T, R> Iterator<R> mapPartitions(Iterator<T> iterator, CheckedFunction<T, R> convertor) {
        return literators2Stream(iterator)
                .map((data) -> Try.of(() -> convertor.apply(data)).getOrElseThrow(TrsSparkException::new))
                .collect(Collectors.toList()).iterator();
    }

    /**
     * 将javaRDD<T> 转换为javaRDD<R>
     *
     * @param rdd       输入的RDD
     * @param convertor 转换行为
     * @return 结果
     */
    public static <T, R> JavaRDD<R> mapPartitions(JavaRDD<T> rdd, CheckedFunction<T, R> convertor) {
        return rdd.mapPartitions((itr) -> mapPartitions(itr, convertor));
    }

    /**
     * 将RDD<T> 转换为RDD<R>
     *
     * @param rdd       输入的RDD
     * @param convertor 转换行为
     * @return 结果
     */
    public static <T, R> RDD<R> mapPartitions(RDD<T> rdd, CheckedFunction<T, R> convertor) {
        return rdd.toJavaRDD().mapPartitions((itr) -> mapPartitions(itr, convertor)).rdd();
    }
}
