package com.trs.spark.util;

import java.util.Objects;

/**
 * @author: zhang.wenquan
 * @description: 配置读取工具
 * @date: 2023/3/22 13:41
 * @version: 1.0
 */
public class ConfigUtils {

    public static String getPropertiesValue(String keyFormat, String... formatValue) {
        return getPropertiesValue(String.format(keyFormat, formatValue));
    }

    public static String getPropertiesValue(String key) {
        String bySysEnv = System.getenv().get(key);
        return Objects.isNull(bySysEnv) ? System.getProperty(key) : bySysEnv;
    }
}
