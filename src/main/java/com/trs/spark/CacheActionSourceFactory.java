package com.trs.spark;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.*;
import com.trs.spark.cache.CacheAction;
import com.trs.spark.cache.action.HdsfCacheAction;
import com.trs.common.datasource.parser.IcebergSourceUrlParser;
import com.trs.common.datasource.parser.RedisParser;

/**
 * <AUTHOR>
 */
public class CacheActionSourceFactory {

    public static CacheAction actionFromDriver(SourceDriver sourceDriver) {
        SourceUrlParser urlParser = sourceDriver.getParser();
        if (urlParser instanceof MongodbDefaultVirtualUrlParser) {
            throw new IllegalArgumentException("没有对应的实现");
        } else if (urlParser instanceof RedisParser) {
            throw new IllegalArgumentException("没有对应的实现");
        } else if (urlParser instanceof HdfsDefaultVirtualUrlParser) {
            return new HdsfCacheAction(sourceDriver);
        } else if (urlParser instanceof HybaseDefaultVirtualUrlParser) {
            throw new IllegalArgumentException("没有对应的实现");
        } else if (urlParser instanceof CommonJdbcParser) {
            throw new IllegalArgumentException("没有对应的实现");
        } else if (urlParser instanceof IcebergSourceUrlParser) {
            throw new IllegalArgumentException("没有对应的实现");
        }
        throw new IllegalArgumentException("无法解析对应的source");
    }
}
