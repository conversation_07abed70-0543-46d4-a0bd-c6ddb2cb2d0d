package com.trs.spark;

import com.trs.common.utils.StringUtils;
import com.trs.spark.configuration.Configurations;
import com.trs.spark.configuration.WhereIsConfig;
import com.trs.spark.constant.RunMode;
import com.trs.spark.exception.TrsSparkException;
import org.apache.spark.SparkContext;
import org.apache.spark.sql.SparkSession;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Spark上下文的创建工具,可以通过该类完成sparkcontext,sqlcontext的创建工作,之后所有的上下文都在此进行初始 <p/>
 * <p>
 * 在spark2.0+中,Spark任务的总入口已经由原有的sparkcontext转变成sparksession,因此,我们的SparkContextHelper的实现也逐步像parkSession靠拢<p/>
 *
 * <AUTHOR>
 */
public class SparkContextHelper {

    /**
     * 我们认为SparkSession只能存在一个
     */
    private static Optional<SparkSession> optSession = Optional.empty();

    private static Boolean hasLoadConfiguration = false;

    /**
     * 创建一个新的SparkSession
     *
     * @param appName spark应用名
     * @param config  配置
     * @param mode    模式
     * @param options 额外参数
     * @return 相关结果
     * @throws TrsSparkException 异常
     */
    public static SparkSession getSparkSession(String appName, WhereIsConfig<?> config, RunMode mode, Optional<Map<String, Object>> options) throws TrsSparkException {
        //如果当前已经初始了SparkSession,直接使用
        if (optSession.isPresent() && validSparkContext()) {
            return optSession.get();
        }
        //否则创建新的SparkSession
        if (!hasLoadConfiguration) {
            //从加载应用的配置内容
            Configurations.getLoader(config).loadIntoSystem(mode);
            hasLoadConfiguration = true;
        }
        //初始SparkSession
        initSparkSession(appName, config, options);

        return optSession.get();
    }

    /**
     * 校验SparkContext
     */
    private static boolean validSparkContext() {
        if (optSession.isPresent()) {
            SparkSession session = optSession.get();
            SparkContext sparkContext = session.sparkContext();
            return sparkContext != null && !sparkContext.isStopped();
        }

        return false;
    }

    private static void initSparkSession(String name, WhereIsConfig config, Optional<Map<String, Object>> options) throws TrsSparkException {
        final String appName = StringUtils.isNullOrEmpty(name) ? String.format("trs_spark_%s", System.currentTimeMillis()) : name;
        try {
            SparkSession session = builder(appName,
                    Configurations.getProperty(Configurations.SPARK_MASTER_PROPERTY).orElseThrow(() -> new TrsSparkException("无法获得spark.master配置")),
                    options.orElse(new HashMap<>(0))).getOrCreate();
            optSession = Optional.of(session);
        } catch (Exception ex) {
            throw new TrsSparkException(ex);
        }

    }

    private static SparkSession.Builder builder(String appName, String master, Map<String, Object> options) {
        SparkSession.Builder builder = SparkSession.builder().appName(appName).master(master);
        options.forEach((key, value) -> builder.config(key, String.valueOf(value)));
        return builder;
    }

}
