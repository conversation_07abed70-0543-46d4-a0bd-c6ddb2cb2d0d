package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.constant.RedisConstants;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.common.datasource.parser.RedisParser;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.util.SourceUtils;
import org.apache.spark.sql.*;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/17 15:16
 * @version: 1.0
 */
public class RedisSource implements BaseSource, RedisConstants {

    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return urlParser instanceof RedisParser;
    }

    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException {
        Dataset<Row> dataset = spark.read()
                .format(PKG)
                .option(OPTION_TABLE, tableName)
                .option(OPTION_HOST, sourceDriver.getHost())
                .option(OPTION_PORT, sourceDriver.getPort())
                .option(OPTION_USER, StringUtils.isNotEmpty(sourceDriver.getUserName()) ? sourceDriver.getUserName() : null)
                .option(OPTION_AUTH, StringUtils.isNotEmpty(sourceDriver.getPassword()) ? sourceDriver.getPassword() : null)
                .load();

        String where = params.getWhere();
        String selectFields = params.getSelectFields();
        //当只检索部分字段
        Column[] columns = SourceUtils.makeColumns(selectFields);
        //当只检索部分字段
        if (columns.length > 0) {
            dataset = dataset.select(columns);
        }
        //设置检索条件
        if (StringUtils.isNotEmpty(where)) {
            dataset = dataset.where(where);
        }
        return dataset;
    }

    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String tableName, Dataset<D> datas, SaveMode saveMode) throws TrsSparkException {
        datas.write()
                .format(PKG)
                .mode(saveMode)
                .option(OPTION_TABLE, tableName)
                .option(OPTION_HOST, sourceDriver.getHost())
                .option(OPTION_PORT, sourceDriver.getPort())
                .option(OPTION_USER, StringUtils.isNotEmpty(sourceDriver.getUserName()) ? sourceDriver.getUserName() : null)
                .option(OPTION_AUTH, StringUtils.isNotEmpty(sourceDriver.getPassword()) ? sourceDriver.getPassword() : null)
                .save();
    }
}
