package com.trs.spark.datasource.condition;

import java.io.Serializable;

/**
 * 检索的其他参数
 * *@author:wen.wen
 * *@create 2023-03-20 16:24
 **/
public class SearchParams implements Serializable {

    /**
     * 检索条件
     */
    private String where;

    /**
     * 检索的字段,默认检索所有
     * 多个用英文分号（;）分隔
     */
    private String selectFields = "*";

    /**
     * 自定义sql，有值的话不再使用where,selectFields
     */
    private String sql;


    public String getWhere() {
        return where;
    }

    public void setWhere(String where) {
        this.where = where;
    }

    public String getSelectFields() {
        return selectFields;
    }

    public void setSelectFields(String selectFields) {
        this.selectFields = selectFields;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }
}
