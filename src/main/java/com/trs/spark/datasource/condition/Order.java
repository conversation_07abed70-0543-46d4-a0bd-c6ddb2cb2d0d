package com.trs.spark.datasource.condition;

import java.io.Serializable;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotNull;
import static com.trs.common.utils.StringUtils.isNullOrEmpty;

/**
 * 排序字段
 * *@author:wen.wen
 * *@create 2023-03-20 16:25
 **/
@Deprecated
public class Order implements Serializable {

    private String fieldName;

    private DIRECTION direction;

    private Order(String fieldName, DIRECTION direction) {
        this.fieldName = checkNotNull(fieldName);
        this.direction = checkNotNull(direction);
    }

    public static Order asc(String fieldName) {
        checkArgument(!isNullOrEmpty(fieldName), "排序字段不能为空");
        return new Order(fieldName, DIRECTION.ASC);
    }

    public static Order desc(String fieldName) {
        checkArgument(!isNullOrEmpty(fieldName), "排序字段不能为空");
        return new Order(fieldName, DIRECTION.DESC);
    }

    public static enum DIRECTION implements Serializable {
        /**
         * 升序
         */
        ASC,
        /**
         * 降序
         */
        DESC;
    }
}
