package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.constant.CatalogConstant;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.common.datasource.parser.IcebergSourceUrlParser;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.util.ConfigUtils;
import com.trs.spark.util.SourceUtils;
import org.apache.spark.sql.*;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.trs.spark.constant.CatalogConstant.TYPE_HADOOP;
import static com.trs.spark.constant.CatalogConstant.TYPE_HIVE;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2023/3/20 16:05
 * @version: 1.0
 */
public class IcebergSource implements BaseSource {

    /**
     * 数据源信息 catalog.dbname.tbname
     */
    private static Pattern SOURCE_INFO = Pattern.compile("(?<catalog>[^\\.]+)\\.(?<dbName>[^\\.]+)\\.(?<tableName>[^\\.]+)");

    private static final String CATALOG_PREFIX = "spark.sql.catalog.";

    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return urlParser instanceof IcebergSourceUrlParser;
    }

    /**
     * 读取数据 https://iceberg.apache.org/docs/latest/spark-queries
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名 catalog.db.table
     * @param params       检索的其他条件
     * @return
     * @throws TrsSparkException
     */
    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException {
        tryInitCateLog(spark, sourceDriver, tableName);
        Dataset<Row> dataset = null;
        if (StringUtils.isNotEmpty(params.getSql())) {
            dataset = spark.sql(params.getSql());
        } else {
            // catalog.db.table
            dataset = spark.table(tableName);
        }
        String where = params.getWhere();
        String selectFields = params.getSelectFields();
        //当只检索部分字段
        Column[] columns = SourceUtils.makeColumns(selectFields);
        //当只检索部分字段
        if (columns != null && columns.length > 0) {
            dataset = dataset.select(columns);
        }
        //设置检索条件
        if (StringUtils.isNotEmpty(where)) {
            dataset = dataset.where(where);
        }
        return dataset;
    }

    /**
     * 写入数据 https://iceberg.apache.org/docs/latest/spark-writes/
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名 catalog.db.table
     * @param datas        数据
     * @param saveMode     保存模式
     * @param <D>
     * @throws TrsSparkException
     */
    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String tableName, Dataset<D> datas, SaveMode saveMode) throws TrsSparkException {
        tryInitCateLog(spark, sourceDriver, tableName);
        try {
            // catalog.db.table
            datas.writeTo(tableName).append();
        } catch (Throwable e) {
            throw new TrsSparkException(e);
        }
    }

    /**
     * 配置 https://iceberg.apache.org/docs/latest/spark-configuration/
     *
     * @param spark
     * @param sourceDriver
     * @return
     */
    public String tryInitCateLog(SparkSession spark, SourceDriver sourceDriver, String tableName) {
        IcebergSourceUrlParser parser = (IcebergSourceUrlParser) sourceDriver.getParser();
        Matcher matcher = SOURCE_INFO.matcher(tableName);
        if (!matcher.find()) {
            throw new IllegalArgumentException("数据源信息配置得不合法，数据源信息: " + tableName);
        }
        String catalogName = matcher.group("catalog");
        // 写入连接信息
        if (spark.conf().contains(CATALOG_PREFIX + catalogName)) {
            return catalogName;
        }
        String catalogType = catalogType(catalogName, parser);
        switch (catalogType) {
            case TYPE_HIVE:
                initHiveCatalogIfNot(spark, catalogName, parser);
                break;
            case TYPE_HADOOP:
                initHadoopCatalogIfNot(spark, catalogName, parser);
                break;
            default:
                throw new IllegalArgumentException(String.format("the type %s of catalog %s is illegal", catalogType, catalogName));
        }
        return catalogName;
    }

    private String catalogType(String catalogName, IcebergSourceUrlParser parser) {
        String url = parser.getUrl();
        if (StringUtils.isNotEmpty(url)) {
            return url.contains("thrift") ? TYPE_HIVE : TYPE_HADOOP;
        }
        // 从url中没拿到就从环境变量中拿
        String type = ConfigUtils.getPropertiesValue(CatalogConstant.TYPE_FORMA, catalogName);
        Objects.requireNonNull(type, String.format("type of catalog %s not found", catalogName));
        return type;
    }


    private void initHadoopCatalogIfNot(SparkSession spark, String catalogName, IcebergSourceUrlParser parser) {
        String url = parser.getUrl();
        if (StringUtils.isEmpty(url)) {
            url = ConfigUtils.getPropertiesValue(CatalogConstant.URL_FORMAT, catalogName);
            if (!url.endsWith(StringUtils.STRING_DIVIDE_FLAG)) {
                url += StringUtils.STRING_DIVIDE_FLAG;
            }
        }
        spark.conf().set(String.format("spark.sql.catalog.%s", catalogName), "org.apache.iceberg.spark.SparkCatalog");
        spark.conf().set(String.format("spark.sql.catalog.%s.type", catalogName), "hadoop");
        spark.conf().set(String.format("spark.sql.catalog.%s.warehouse", catalogName), url);
    }


    private void initHiveCatalogIfNot(SparkSession spark, String catalogName, IcebergSourceUrlParser parser) {
        String url = StringUtils.isEmpty(parser.getUrl()) ? ConfigUtils.getPropertiesValue(CatalogConstant.URL_FORMAT, catalogName) : parser.getUrl();
        spark.conf().set(String.format("spark.sql.catalog.%s", catalogName), "org.apache.iceberg.spark.SparkCatalog");
        spark.conf().set(String.format("spark.sql.catalog.%s.type", catalogName), "hive");
        spark.conf().set(String.format("spark.sql.catalog.%s.uri", catalogName), url);
    }
}
