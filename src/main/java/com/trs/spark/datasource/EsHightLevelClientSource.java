package com.trs.spark.datasource;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.trs.common.config.ConfigTemplate;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.configuration.CommonConnectParam;
import com.trs.spark.connect.CustomConnectionKeepAliveStrategy;
import com.trs.spark.constant.ConfigureConstant;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.parser.EsHlcParser;
import com.trs.spark.util.SourceUtils;
import io.vavr.control.Either;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.*;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import javax.annotation.Nonnull;
import javax.net.ssl.SSLContext;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Stream;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@Slf4j
public class EsHightLevelClientSource implements BaseSource{
    /**
     * 默认连接时间 单位毫秒
     */
    static Integer defaultConeTimeOut = 30000;

    /**
     * 默认读取时间 单位毫秒
     */
    static Integer defaultReadTimeout = 50000;

    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return urlParser instanceof EsHlcParser;
    }

    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException {
        RestHighLevelClient highLevelClient = getHighLevelClient(sourceDriver.getUserName(), sourceDriver.getPassword(), sourceDriver.getUrl(), new CommonConnectParam(), new HashMap<String, String>(16));
        Dataset<Row> dataset = queryDataByRestClient(highLevelClient, spark, tableName, params);
        try {
            highLevelClient.close();
        } catch (IOException e) {
            throw new TrsSparkException(e);
        }
        return dataset;
    }

    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String tableName, Dataset<D> datas, SaveMode saveMode) throws TrsSparkException {
        List<D> collect = datas.toJavaRDD().collect();
        // 补充id字段
        String idField = null;
        if (Objects.nonNull(sourceDriver.getUrlParams())) {
            for (String key : sourceDriver.getUrlParams().getKeySet()) {
                if("es.mapping.id".equals(key)){
                    idField = sourceDriver.getUrlParams().getProperty(key);
                }
            }
        }
        // 分批次写入
        Integer perCountStr = Integer.valueOf(System.getProperty("esHlc.write.count", "5000"));
        String finalIdField = idField;
        Lists.partition(collect, perCountStr).forEach(list -> {
            try {
                writeDataToSource(list, finalIdField, tableName, sourceDriver);
            } catch (Exception e) {
                throw new TrsSparkException(e);
            }
        });
    }

    private <D> void writeDataToSource(List<D> collect, String idField, String tableName, SourceDriver sourceDriver) throws Exception {
        BulkRequest request = new BulkRequest();
        String finalIdField = idField;
        collect.forEach(data -> {
            IndexRequest indexRequest = new IndexRequest(tableName).source(JSONObject.toJSONString(data), XContentType.JSON);
            if(finalIdField != null){
                indexRequest.id(JSONObject.parseObject(JSONObject.toJSONString(data)).getString(finalIdField));
            }
            request.add(indexRequest);
        });
        RestHighLevelClient highLevelClient = getHighLevelClient(sourceDriver.getUserName(), sourceDriver.getPassword(), sourceDriver.getUrl(), new CommonConnectParam(), new HashMap<String, String>(16));
        try {
            BulkResponse response = highLevelClient.bulk(request, RequestOptions.DEFAULT);
            if (!RestStatus.OK.equals(response.status())) {
                throw new TrsSparkException(response.buildFailureMessage());
            }
        } catch (IOException e) {
            throw new TrsSparkException(e);
        } finally {
            highLevelClient.close();
        }
    }

    private RestHighLevelClient getHighLevelClient(String username, String password, String url, CommonConnectParam connectParam, Map<String, String> customHeaders) {
        int i = url.indexOf("?");
        if (i > 0){
            url = url.substring(0, i);
        }
        String str = username + ":" + password;
        String auth = Base64.getEncoder().encodeToString(str.getBytes());
        customHeaders.put("Authorization", "Basic " + auth);
        log.info("注入了认证信息[{}]", customHeaders);
        RestClientBuilder builder = getRestClientBuilder(username, password, url, connectParam, customHeaders);
        return new RestHighLevelClient(builder);
    }

    /**
     * 获取RestClientBuilder
     *
     * @param username     用户名
     * @param password     密码
     * @param url          服务地址
     * @param connectParam 连接参数
     * @return RestClientBuilder
     */
    static RestClientBuilder getRestClientBuilder(String username, String password, String url, @Nonnull CommonConnectParam connectParam, Map<String, String> customHeaders) {
        ConfigTemplate configTemplate = ConfigTemplate.buildDefaultConfigTemplate();
        //处理连接地址
        HttpHost[] httpHosts = Stream.of(url.split("[,;]")).map(one -> {
            String hostAndPort = one.split("//")[1];
            String host = hostAndPort.split(":")[0];
            String port = hostAndPort.split(":")[1];
            checkArgument(!StringUtils.isNullOrEmpty(host) && !StringUtils.isNullOrEmpty(port));
            return new HttpHost(host, Integer.parseInt(port), connectParam.getProperty(ConfigureConstant.DEFAULT_CONNECTION_PROTOCOL_KEY, "https"));
        }).toArray(HttpHost[]::new);
        RestClientBuilder builder = RestClient.builder(httpHosts);
        var clientConfigCallback = Optional.of(connectParam)
                .map(it -> it.getPropertyAsObject(ConfigureConstant.ES_REPOSITORY_CONNECTION_HTTP_CLIENT_CONFIG_CALLBACK))
                .map(RestClientBuilder.HttpClientConfigCallback.class::cast);
        if (clientConfigCallback.isPresent()) {
            builder.setHttpClientConfigCallback(clientConfigCallback.get());
        } else {
            //用户信息
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
            //requestConfig
            RequestConfig.Builder requestConfigBuilder = RequestConfig.custom()
                    .setConnectTimeout(configTemplate.getPropertyAsNumber("es.connect.timeout", defaultConeTimeOut, Integer.class))
                    .setSocketTimeout(configTemplate.getPropertyAsNumber("es.read.timeout", defaultReadTimeout, Integer.class))
                    .setConnectionRequestTimeout(configTemplate.getPropertyAsNumber("es.read.timeout", defaultReadTimeout, Integer.class));
            builder.setHttpClientConfigCallback(httpAsyncClientBuilder -> {
                httpAsyncClientBuilder
                        .disableAuthCaching()
                        .setDefaultRequestConfig(requestConfigBuilder.build())
                        .setMaxConnTotal(configTemplate.getPropertyAsNumber("es.connect.max", 100, Integer.class))
                        .setMaxConnPerRoute(configTemplate.getPropertyAsNumber("es.connect.max.per.route", 50, Integer.class))
                        .setDefaultCredentialsProvider(credentialsProvider);
                //add header
                List<Header> headers = new ArrayList<>();
                headers.add(new BasicHeader(HTTP.CONN_DIRECTIVE, HTTP.CONN_KEEP_ALIVE));
                headers.add(new BasicHeader(HTTP.CONN_KEEP_ALIVE, "720"));
                Optional.ofNullable(customHeaders)
                        .ifPresent(map -> map.forEach((key, value) -> headers.add(new BasicHeader(key, value))));
                httpAsyncClientBuilder.setDefaultHeaders(headers);
                httpAsyncClientBuilder.setKeepAliveStrategy(CustomConnectionKeepAliveStrategy.INSTANCE());
                //如果是https协议，则需要构建HTTPS的上线文
                if (connectParam.isHttpsProtocol()) {
                    SSLContext sslContext = buildSSLContext(password, connectParam)
                            .getOrElseThrow(e ->
                                    new IllegalArgumentException("JestClient can not be created  by ssl, because " + e.getMessage(), e)
                            );
                    httpAsyncClientBuilder.setSSLContext(sslContext).setSSLHostnameVerifier((hostname, session) -> true);
                }
                return httpAsyncClientBuilder;
            });
        }

        return builder;
    }

    /**
     * 构建SSLContext
     *
     * @param password     密码
     * @param connectParam 连接信息
     * @return SSLContext
     */
    static Either<Throwable, SSLContext> buildSSLContext(String password, CommonConnectParam connectParam) {
        if (!connectParam.isHttpsProtocol()) {
            Either.left(new IllegalArgumentException("net protocol is not https"));
        }
        return Try.of(() -> {
            KeyStore trustStore = KeyStore.getInstance("jks");
            trustStore.load(null, null);
            if (connectParam.containCertificate()) {
                String caContent = connectParam.getProperty(ConfigureConstant.DEFAULT_HTTPS_SSL_CONTENT_KEY);
                CertificateFactory cfactory =
                        CertificateFactory.getInstance("X.509");

                InputStream inputStream = caContent.toLowerCase().endsWith("jks") ?
                        Files.newInputStream(Paths.get(caContent)) :    //读取jks文件
                        new ByteArrayInputStream(caContent.getBytes()); //读取jks内容

                Certificate trustedCa = cfactory.generateCertificate(inputStream);

                trustStore.setCertificateEntry("ca", trustedCa);
            }
            return new SSLContextBuilder().loadTrustMaterial(trustStore, (X509Certificate[] x509Certificates, String s) -> true).build();

        }).toEither();
    }

    public Dataset<Row> queryDataByRestClient(RestHighLevelClient highLevelClient, SparkSession spark, String tableName, SearchParams params) {
        Dataset<Row> dataset = null;

        try {
            // query data by scroll api, avoid the OOM
            SearchRequest searchRequest = new SearchRequest(tableName);
            final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));
            searchRequest.scroll(scroll);

            // set the size of result, note: if the number of size was too large, may cause the OOM
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .size(10000);
            String selectFields = params.getSelectFields();
            //当只检索部分字段
            String[] columns = SourceUtils.makeColumnStrs(selectFields);
            if (columns.length > 0) {
                searchSourceBuilder.fetchSource(columns, null);
            }
            //设置检索条件
            String where = params.getWhere();
            if (StringUtils.isNotEmpty(where)) {
                searchSourceBuilder.query(QueryBuilders.wrapperQuery(where));
            }
            searchRequest.source(searchSourceBuilder);
            //查询数据
            SearchResponse searchResponse = highLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            // 返回空
            if(searchHits==null || searchHits.length == 0){
                dataset = spark.emptyDataFrame();
            }
            // 构建数据结构
            String[] cols = SourceUtils.makeFinalColumnStrs(selectFields);
            StructType structType = buildStructType(searchHits, cols);
            StructField[] fields = structType.fields();
            List<Row> rows = new ArrayList<>();
            String scrollId = searchResponse.getScrollId();
            // 转换数据
            while (searchHits != null && searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    Object[] values = new Object[fields.length];
                    for (int i = 0; i < fields.length; i++) {
                        values[i] = source.get(fields[i].name());
                    }
                    rows.add(RowFactory.create(values));
                }
                // continue scroll search
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                searchResponse = highLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
            }

            JavaSparkContext sparkContext = new JavaSparkContext(spark.sparkContext());
            JavaRDD<Row> rowRDD = sparkContext.parallelize(rows);
            dataset = spark.createDataFrame(rowRDD, structType);
        } catch (IOException e) {
            log.error("***** Query data failed, exception occurred.", e);
        }

        return dataset;
    }

    /**
     * 动态构建schema
     *
     */
    private StructType buildStructType(SearchHit[] searchHits, String[] columns) {
        log.info("***** Build schema for columns: {}", JSONObject.toJSONString(columns));
        Map<String, StructField> fields = new HashMap<>(16);
        for (String column : columns) {
            Boolean isFound = false;
            for (SearchHit searchHit : searchHits) {
                Map<String, Object> source = searchHit.getSourceAsMap();
                Object value = source.get(column);
                if(value != null){
                    DataType dataType = inferDataType(value);
                    fields.put(column, DataTypes.createStructField(column, dataType, true));
                    isFound = true;
                    break;
                }
            }
            if(!isFound){
                fields.put(column, DataTypes.createStructField(column, DataTypes.StringType, true));
            }
        }

        return DataTypes.createStructType(fields.values().stream().toArray(StructField[]::new));
    }

    /**
     * 根据值推断数据类型
     *
     * @param value value
     * @return DataType
     */
    private DataType inferDataType(Object value) {
        if (value instanceof Integer) {
            return DataTypes.IntegerType;
        } else if (value instanceof Long) {
            return DataTypes.LongType;
        } else if (value instanceof Double) {
            return DataTypes.DoubleType;
        } else if (value instanceof Boolean) {
            return DataTypes.BooleanType;
        } else if (value instanceof Date) {
            return DataTypes.DateType;
        } else if (value instanceof Timestamp) {
            return DataTypes.TimestampType;
        } else {
            return DataTypes.StringType;
        }
    }
}
