package com.trs.spark.datasource;

import com.mongodb.spark.MongoSpark;
import com.mongodb.spark.config.ReadConfig;
import com.mongodb.spark.config.WriteConfig;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.MongodbDefaultVirtualUrlParser;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.util.SourceUtils;
import io.vavr.control.Try;
import org.apache.spark.sql.*;
import scala.collection.JavaConverters;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
public class MongodbSource implements BaseSource {
    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return urlParser instanceof MongodbDefaultVirtualUrlParser;
    }

    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException {
        Optional<Map<String, String>> configMap = sourceDriverToConfigMap(sourceDriver, tableName, true);
        if (!configMap.isPresent()) {
            return spark.emptyDataFrame();
        }
        ReadConfig readConfig = ReadConfig.create(configMap.get());
        Dataset<Row> dataset = MongoSpark.loadAndInferSchema(spark, readConfig);
        String where = params.getWhere();
        String selectFields = params.getSelectFields();
        //当只检索部分字段
        Column[] columns = SourceUtils.makeColumns(selectFields);
        //当只检索部分字段
        if (columns.length > 0) {
            dataset = dataset.select(columns);
        }
        //设置检索条件
        if (StringUtils.isNotEmpty(where)) {
            dataset = dataset.where(where);
        }
        return dataset;
    }

    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String tableName, Dataset<D> datas, SaveMode saveMode) throws TrsSparkException {
        sourceDriverToConfigMap(sourceDriver, tableName, false).ifPresent(configMap -> {
            WriteConfig writeConfig = WriteConfig.create(configMap);
            MongoSpark.save(datas, writeConfig);
        });
    }

    private Optional<Map<String, String>> sourceDriverToConfigMap(SourceDriver sourceDriver, String tableName, Boolean isRead) {
        try {
            if (sourceDriver.getParser() instanceof MongodbDefaultVirtualUrlParser) {
                MongodbDefaultVirtualUrlParser parser = (MongodbDefaultVirtualUrlParser) sourceDriver.getParser();
                String username = sourceDriver.getUserName();
                String password = sourceDriver.getPassword();
                Optional<String> auth = null;
                if (StringUtils.isNullOrEmpty(username)) {
                    auth = Optional.empty();
                } else if (StringUtils.isNullOrEmpty(password)) {
                    String user = Try.of(() -> URLEncoder.encode(username, "UTF-8")).getOrElseThrow((e) -> new IllegalArgumentException(e));
                    auth = Optional.of(user + "@");
                } else {
                    String user = Try.of(() -> URLEncoder.encode(username, "UTF-8")).getOrElseThrow((e) -> new IllegalArgumentException(e));
                    String pass = Try.of(() -> URLEncoder.encode(password, "UTF-8")).getOrElseThrow((e) -> new IllegalArgumentException(e));
                    auth = Optional.of(String.format("%s:%s@", user, pass));
                }
                Map<String, String> configMap = new HashMap<>(3);
                String value = String.format("mongodb://%s%s/%s%s",
                        auth.orElse(""),
                        JavaConverters.mapAsScalaMap(parser.getPairsOfIPAndPort()).mkString(",").replaceAll(" -> ", ":"),
                        tableName,
                        auth.isPresent() ? "?authSource=admin" : "");
                if (isRead) {
                    configMap.put("spark.mongodb.input.uri", value);
                } else {
                    configMap.put("spark.mongodb.output.uri", value);
                }
                configMap.put("spark.mongodb.keep_alive_ms", "500000");

                return Optional.of(buildConfig == null ? configMap : buildConfig.apply(configMap));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    private Function<Map<String, String>, Map<String, String>> buildConfig = null;

    public MongodbSource(Function<Map<String, String>, Map<String, String>> buildConfig) {
        this.buildConfig = buildConfig;
    }

    public MongodbSource() {
    }
}
