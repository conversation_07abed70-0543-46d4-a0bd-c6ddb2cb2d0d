package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.HdfsDefaultVirtualUrlParser;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.function.CheckedFunction;
import com.trs.spark.util.SourceUtils;
import io.vavr.control.Try;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.*;

import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
public class HDFSSource implements BaseSource {

    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return urlParser instanceof HdfsDefaultVirtualUrlParser;
    }

    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String file, SearchParams params) throws TrsSparkException {
        Dataset<Row> dataset = loadDataFromSource(spark, sourceDriver, new String[]{file});
        String where = params.getWhere();
        String selectFields = params.getSelectFields();
        Column[] columns = SourceUtils.makeColumns(selectFields);
        //当只检索部分字段
        if (columns.length > 0) {
            dataset = dataset.select(columns);
        }
        //设置检索条件
        if (StringUtils.isNotEmpty(where)) {
            dataset = dataset.where(where);
        }
        return dataset;
    }

    public <T> JavaRDD<T> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, CheckedFunction<Dataset<Row>, JavaRDD<T>> convertor, String... fileName) throws TrsSparkException {
        Dataset<Row> dataset = loadDataFromSource(spark, sourceDriver, fileName);
        return Try.of(() -> convertor.apply(dataset)).getOrElseThrow((throwable) -> new TrsSparkException("loadData fail when excute convert", throwable));
    }

    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String[] files) throws TrsSparkException {
        String url = sourceDriver.getUrl();

        FileSystem fs = Try.of(() -> FileSystem.get(new URI(url), new Configuration())).getOrElseThrow(e -> new TrsSparkException(e));
        String[] filterFiles = Arrays.stream(files)
                .map(file -> url + file)
                .filter(file -> Try.of(() -> fs.exists(new Path(file))).getOrElseThrow((e) -> new IllegalArgumentException(e)))
                .toArray(String[]::new);

        if (filterFiles.length < 1) {
            throw new RuntimeException("在HDFS中传入的相关文件集" + String.join(";", files) + "，请检查相关数据源!");
        } else {
            String fileType = getFileType(filterFiles);
            switch (fileType.toLowerCase()) {
                case "json":
                    return spark.read().json(filterFiles);
                case "csv":
                    return spark.read().csv(filterFiles);
                case "parquet":
                    return spark.read().parquet(filterFiles);
                case "orc":
                    return spark.read().orc(filterFiles);
                default:
                    return spark.read().text(filterFiles);
            }
        }
    }

    public String getFileType(String[] files) {
        List fileNames = Arrays.stream(files).map(name -> {
            if (name.indexOf(StringUtils.STRING_DOT) > 0 && !name.endsWith(StringUtils.STRING_DOT)) {
                return Optional.of(name.substring(name.lastIndexOf(StringUtils.STRING_DOT) + 1));
            } else {
                return Optional.empty();
            }
        }).filter(Optional::isPresent).map(Optional::get).distinct().collect(Collectors.toList());
        if (fileNames.size() == 1) {
            return String.valueOf(fileNames.get(0));
        } else {
            throw new RuntimeException("暂不支持多类型文件加载");
        }
    }

    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String pathName, Dataset<D> datas, SaveMode saveMode) throws TrsSparkException {
        datas.write()
                .mode(saveMode)
                .format(getFileType(new String[]{pathName}))
                .save(sourceDriver.getUrl() + pathName);
    }
}