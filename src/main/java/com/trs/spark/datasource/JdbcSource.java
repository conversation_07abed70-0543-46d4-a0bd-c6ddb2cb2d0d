package com.trs.spark.datasource;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.CommonJdbcParser;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.util.SourceUtils;
import org.apache.spark.sql.*;

import java.util.Properties;

import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * jdbc捞取/插入 数据实现
 * *@author:wen.wen
 * *@create 2023-03-17 10:12
 **/
public class JdbcSource implements BaseSource {

    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return urlParser instanceof CommonJdbcParser;
    }

    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException {
        Properties properties = getProperties(sourceDriver);
        SourceUrlParser urlParser = sourceDriver.getParser();
        PreConditionCheck.checkArgument(CommonJdbcParser.class.isAssignableFrom(urlParser.getClass()),
                urlParser.getClass().getCanonicalName() + "parser does not match CommonJdbcParser");
        CommonJdbcParser parser = (CommonJdbcParser) urlParser;
        Dataset<Row> dataset = spark.read().option("driver", parser.getDriver()).jdbc(sourceDriver.getUrl(), tableName, properties);

        String where = params.getWhere();
        String selectFields = params.getSelectFields();
        //当只检索部分字段
        Column[] columns = SourceUtils.makeColumns(selectFields);
        //当只检索部分字段
        if (columns.length > 0) {
            dataset = dataset.select(columns);
        }
        //设置检索条件
        if (StringUtils.isNotEmpty(where)) {
            dataset = dataset.where(where);
        }
        return dataset;
    }

    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String tableName, Dataset<D> datas, SaveMode saveMode) {
        Properties properties = getProperties(sourceDriver);
        SourceUrlParser urlParser = sourceDriver.getParser();
        PreConditionCheck.checkArgument(CommonJdbcParser.class.isAssignableFrom(urlParser.getClass()),
                urlParser.getClass().getCanonicalName() + "parser does not match CommonJdbcParser");
        CommonJdbcParser parser = (CommonJdbcParser) sourceDriver.getParser();
        datas.write().mode(saveMode).option("driver", parser.getDriver()).jdbc(sourceDriver.getUrl(), tableName, properties);
    }

    private Properties getProperties(SourceDriver sourceDriver) {
        Properties properties = new Properties();
        properties.put("user", checkNotNull(sourceDriver.getUserName()));
        properties.put("password", checkNotNull(sourceDriver.getPassword()));
        return properties;
    }
}
