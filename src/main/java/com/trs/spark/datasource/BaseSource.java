package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.function.CheckedFunction;
import com.trs.spark.util.StreamUtils;
import io.vavr.control.Try;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.*;

import java.io.Serializable;

/**
 * 数据源基类
 * 定义检索和入库的基本行为
 * *@author:wen.wen
 * *@create 2023-03-16 16:11
 **/
public interface BaseSource extends Serializable {

    /**
     * supportSourceUrlParser<BR>
     *
     * @param urlParser 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/15 17:55
     */
    Boolean supportSourceUrlParser(SourceUrlParser urlParser);

    /**
     * 从Mysql数据库加载数据,并将数据转换成
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名
     * @return 结果
     * @throws TrsSparkException 异常
     */
    default Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName) throws TrsSparkException {
        return loadDataFromSource(spark, sourceDriver, tableName, new SearchParams());
    }

    /**
     * 从Mysql数据库加载数据,并将数据转换成
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名
     * @param params       检索的其他条件
     * @return 结果
     * @throws TrsSparkException 异常
     */
    Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException;

    /**
     * 从Mysql数据库加载数据,并将数据转换成需要的类型
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名
     * @param convertor    转换
     * @return 结果
     * @throws TrsSparkException 异常
     */
    default <T> JavaRDD<T> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName,
                                              CheckedFunction<Dataset<Row>, JavaRDD<T>> convertor) throws TrsSparkException {
        Dataset<Row> dataset = loadDataFromSource(spark, sourceDriver, tableName);
        return Try.of(() -> convertor.apply(dataset)).getOrElseThrow((throwable) -> new TrsSparkException("loadData fail when excute convert", throwable));
    }


    /**
     * 将数据{@code datas}写入到数据库中
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名
     * @param datas        数据
     * @param saveMode     保存模式
     * @throws TrsSparkException 异常
     */
    <D> void writeDataToSource(SparkSession spark,
                               SourceDriver sourceDriver,
                               String tableName,
                               Dataset<D> datas,
                               SaveMode saveMode) throws TrsSparkException;


    /**
     * 将数据{@code datas}写入到数据库中
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名
     * @param datas        数据
     * @param saveMode     保存模式
     * @param convertor    转换器
     * @param targetClass  目标类类型
     * @throws TrsSparkException 异常
     */
    default <R, D> void writeDataToSource(
            SparkSession spark,
            SourceDriver sourceDriver,
            String tableName,
            JavaRDD<R> datas,
            SaveMode saveMode,
            CheckedFunction<R, D> convertor,
            Class<D> targetClass) throws TrsSparkException {
        JavaRDD<D> javaRdd = StreamUtils.mapPartitions(datas, convertor);
        Dataset<D> dataset = spark.createDataset(javaRdd.rdd(), Encoders.bean(targetClass));
        writeDataToSource(spark, sourceDriver, tableName, dataset, saveMode);
    }
}
