package com.trs.spark.datasource;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.HybaseDefaultVirtualUrlParser;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.hybase.client.TRSConnection;
import com.trs.hybase.client.TRSDatabase;
import com.trs.hybase.client.TRSRecord;
import com.trs.hybase.hadoop.common.HybaseInputFormat;
import com.trs.hybase.hadoop.common.HybaseSparkUtils;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.function.Function;
import com.trs.spark.util.HybaseUtils;
import com.trs.spark.util.StreamUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.io.LongWritable;
import org.apache.spark.SparkContext;
import org.apache.spark.rdd.RDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.types.StructType;
import scala.Tuple2;

import java.util.Optional;

import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * 海贝Source
 * *@author:wen.wen
 * *@create 2023-03-17 10:38
 **/
public class HybaseSource implements BaseSource {

    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return urlParser instanceof HybaseDefaultVirtualUrlParser;
    }

    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException {
        try {
            Optional<TRSDatabase> optionalTRSDatabase = HybaseUtils.getDatabase(sourceDriver, tableName);
            if (!optionalTRSDatabase.isPresent()) {
                throw new NullPointerException(tableName + "is not exists in Hybase !");
            }
            //构建结构类型
            StructType structType = HybaseSparkUtils.buildSchema(optionalTRSDatabase.get(), StringUtils.STRING_MULTIPLY_FLAG);
            //从海贝中捞取数据
            RDD<TRSRecord> recordRdd = loadDataFromSourceAsRecord(spark, sourceDriver, (configuration) -> {
                configuration.set(HybaseInputFormat.HYBASE_DB_TABLE, tableName);
                //获得所有字段
                if (!StringUtils.isNullOrEmpty(params.getSelectFields())) {
                    configuration.set(HybaseInputFormat.HYBASE_DB_RECORDFIELDS, params.getSelectFields());
                }
                //利用保留字段获得所有的数据
                if (!StringUtils.isNullOrEmpty(params.getWhere())) {
                    configuration.set(HybaseInputFormat.HYBASE_DB_QUERY, params.getWhere());
                }
                return configuration;
            });
            //将捞取到的结果转换成Row形式
            RDD<Row> rowRdd = StreamUtils.mapPartitions(recordRdd, HybaseSparkUtils::record2Row);
            //创建dataSet
            return spark.createDataFrame(rowRdd, structType);
        } catch (Exception e) {
            throw new TrsSparkException("loadData from hybase error ", e);
        }
    }

    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String tableName, Dataset<D> datas, SaveMode saveMode) throws TrsSparkException {
        try {
            TRSConnection connection = HybaseUtils.getConnectionFunction(sourceDriver);
            //检查表是否存在
            PreConditionCheck.checkArgument(HybaseUtils.databaseExists(tableName, connection), String.format("表名为:%s的表不存在", tableName));
            HybaseUtils.save(sourceDriver, tableName, datas).getOrElseThrow((e) -> e);
        } catch (Exception e) {
            throw new TrsSparkException("将数据写入到hybase时出现错误,错误信息为:" + e.getMessage(), e);
        }
    }

    /**
     * 从海贝中捞取数据
     *
     * @param spark               SparkSession
     * @param sourceDriver        SparkSession
     * @param doOtherConditionSet 其他配置信息设置行为
     */
    public RDD<TRSRecord> loadDataFromSourceAsRecord(SparkSession spark, SourceDriver sourceDriver, Function<Configuration, Configuration> doOtherConditionSet) {
        SparkContext sparkContext = spark.sparkContext();
        Configuration configuration = buildHybaseConfig(sourceDriver, doOtherConditionSet);
        RDD<Tuple2<LongWritable, TRSRecord>> rdd = sparkContext.newAPIHadoopRDD(configuration, HybaseInputFormat.class, LongWritable.class, TRSRecord.class);
        return StreamUtils.mapPartitions(rdd, Tuple2::_2);
    }

    /**
     * 构建海贝配置信息
     *
     * @param sourceDriver        sourceDriver
     * @param doOtherConditionSet 执行其他的条件设置
     */
    private Configuration buildHybaseConfig(SourceDriver sourceDriver, Function<Configuration, Configuration> doOtherConditionSet) {
        String host = checkNotNull(sourceDriver.getHost());
        String port = checkNotNull(sourceDriver.getPort());
        String userName = checkNotNull(sourceDriver.getUserName());
        String password = checkNotNull(sourceDriver.getPassword());
        Configuration configuration = new Configuration();
        configuration.set(HybaseInputFormat.HYBASE_DB_HOST, host);
        configuration.set(HybaseInputFormat.HYBASE_DB_PORT, port);
        configuration.set(HybaseInputFormat.HYBASE_DB_USERNAME, userName);
        configuration.set(HybaseInputFormat.HYBASE_DB_PASSWORD, password);
        //设置其他检索条件
        return doOtherConditionSet.apply(configuration);
    }
}
