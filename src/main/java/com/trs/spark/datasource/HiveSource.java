package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.function.CheckedFunction;
import com.trs.spark.util.SourceUtils;
import com.trs.spark.util.StreamUtils;
import org.apache.commons.beanutils.BeanMap;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.*;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * HiveSource
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/2/4 16:53
 * @since 1.0
 */
public class HiveSource implements BaseSource {

    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return false;
    }

    /**
     * 读取数据 https://iceberg.apache.org/docs/latest/spark-queries
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名 catalog.db.table
     * @param params       检索的其他条件
     * @return
     * @throws TrsSparkException
     */
    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException {
        Dataset<Row> dataset;
        if (StringUtils.isNotEmpty(params.getSql())) {
            dataset = spark.sql(params.getSql());
        } else {
            // catalog.db.table
            dataset = spark.table(tableName);
        }
        String where = params.getWhere();
        String selectFields = params.getSelectFields();
        //当只检索部分字段
        Column[] columns = SourceUtils.makeColumns(selectFields);
        //当只检索部分字段
        if (columns != null && columns.length > 0) {
            dataset = dataset.select(columns);
        }
        //设置检索条件
        if (StringUtils.isNotEmpty(where)) {
            dataset = dataset.where(where);
        }
        return dataset;
    }


    /**
     * 将数据{@code datas}写入到数据库中
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名
     * @param datas        数据
     * @param saveMode     保存模式
     * @param convertor    转换器
     * @param targetClass  目标类类型
     * @throws TrsSparkException 异常
     */
    @Override
    public <R, D> void writeDataToSource(
            SparkSession spark,
            SourceDriver sourceDriver,
            String tableName,
            JavaRDD<R> datas,
            SaveMode saveMode,
            CheckedFunction<R, D> convertor,
            Class<D> targetClass) throws TrsSparkException {
        JavaRDD<D> javaRdd = StreamUtils.mapPartitions(datas, convertor);
        StructType structType = Encoders.bean(targetClass).schema();
        StructField[] structFields = structType.fields();
        // 排序
        Map<String, StructField> structFieldMap = Arrays.stream(structFields).collect(Collectors.toMap(StructField::name, v -> v));
        Field[] fields = targetClass.getDeclaredFields();
        Arrays.stream(fields).map(field -> structFieldMap.get(field.getName()))
                .collect(Collectors.toList())
                .toArray(structFields);
        JavaRDD<Row> javaRDD = javaRdd.map((entity) -> {
            BeanMap beanMap = new BeanMap(entity);
            Object[] values = Arrays.stream(structFields)
                    //将数据根据数据库字段名称获取对应的值
                    .map((structField) -> beanMap.get(structField.name()))
                    .toArray();
            return RowFactory.create(values);
        });
        Dataset<Row> dataset = spark.createDataFrame(javaRDD, structType);
        writeDataToSource(spark, sourceDriver, tableName, dataset, saveMode);
    }

    /**
     * 写入数据
     *
     * @param spark        sparkSession
     * @param sourceDriver 数据库驱动
     * @param tableName    表名 catalog.db.table
     * @param datas        数据
     * @param saveMode     保存模式
     * @param <D>
     * @throws TrsSparkException
     */
    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String tableName, Dataset<D> datas, SaveMode saveMode) throws TrsSparkException {
        datas.write()
                .mode(saveMode)
                .format("hive")
                .insertInto(tableName);
    }
}
