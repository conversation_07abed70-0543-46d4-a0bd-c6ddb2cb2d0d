package com.trs.spark.datasource;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.parser.SourceUrlParser;
import com.trs.common.utils.StringUtils;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.common.datasource.parser.EsParser;
import com.trs.spark.exception.TrsSparkException;
import com.trs.spark.util.SourceUtils;
import org.apache.spark.sql.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 海贝Source
 * *@author:wen.wen
 * *@create 2023-03-17 10:38
 **/
public class EsSource implements BaseSource {

    private Map<String, String> makeCommonMap(SourceDriver sourceDriver) {
        final Map<String, String> map = new HashMap<>(4);
        map.put("es.nodes", sourceDriver.getHost());
        map.put("es.port", sourceDriver.getPort());
        map.put("es.net.http.auth.user", sourceDriver.getUserName());
        map.put("es.net.http.auth.pass", sourceDriver.getPassword());
        map.put("es.nodes.wan.only", "true");
        map.put("es.mapping.date.rich", "false");
        if (Objects.nonNull(sourceDriver.getUrlParams())) {
            // 补充URL参数
            for (String key : sourceDriver.getUrlParams().getKeySet()) {
                map.put(key, sourceDriver.getUrlParams().getProperty(key));
            }
        }
        return map;
    }

    @Override
    public Boolean supportSourceUrlParser(SourceUrlParser urlParser) {
        return urlParser instanceof EsParser;
    }

    @Override
    public Dataset<Row> loadDataFromSource(SparkSession spark, SourceDriver sourceDriver, String tableName, SearchParams params) throws TrsSparkException {
        final Map<String, String> map = makeCommonMap(sourceDriver);
        Dataset<Row> dataset = spark.read().format("org.elasticsearch.spark.sql")
                .options(map)
                .load(tableName);
        String where = params.getWhere();
        String selectFields = params.getSelectFields();
        //当只检索部分字段
        Column[] columns = SourceUtils.makeColumns(selectFields);
        //当只检索部分字段
        if (columns.length > 0) {
            dataset = dataset.select(columns);
        }
        //设置检索条件
        if (StringUtils.isNotEmpty(where)) {
            dataset = dataset.where(where);
        }
        return dataset;
    }

    @Override
    public <D> void writeDataToSource(SparkSession spark, SourceDriver sourceDriver, String tableName, Dataset<D> datas, SaveMode saveMode) throws TrsSparkException {
        final Map<String, String> map = makeCommonMap(sourceDriver);
        datas.write().format("org.elasticsearch.spark.sql")
                .options(map)
                .mode(saveMode)
                .save(tableName);
    }
}
