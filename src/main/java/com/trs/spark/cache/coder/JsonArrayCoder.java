package com.trs.spark.cache.coder;

import com.trs.spark.cache.CacheCoder;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class JsonArrayCoder implements CacheCoder<String> {

    @Override
    public Dataset<String> enCode(SparkSession spark, Dataset<Row> source) {
        String json = source.collectAsList().stream()
                .map(Row::json)
                .collect(Collectors.joining(","));
        json = "[" + json + "]";
        Dataset<String> cacheDs = spark.createDataset(Arrays.asList(json), Encoders.STRING());
        return cacheDs;
    }

    @Override
    public Dataset<Row> deCode(SparkSession spark, Dataset<Row> source) {
        List<Row> rows = source.takeAsList(1);
        if (rows != null && rows.size() > 0) {
            String jsonStr = rows.get(0).getString(0);
            Dataset<Row> ds = spark.read().json(spark.createDataset(Collections.singletonList(jsonStr), Encoders.STRING()));
            return ds;
        }
        return null;
    }
}
