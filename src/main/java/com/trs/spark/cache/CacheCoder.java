package com.trs.spark.cache;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public interface CacheCoder<T extends Serializable> extends Serializable {

    /**
     * enCode<BR>
     *
     * @param spark  参数
     * @param source 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/8/15 16:52
     */
    Dataset<T> enCode(SparkSession spark, Dataset<Row> source);

    /**
     * deCode<BR>
     *
     * @param spark  参数
     * @param source 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/8/15 16:52
     */
    Dataset<Row> deCode(SparkSession spark, Dataset<Row> source);
}
