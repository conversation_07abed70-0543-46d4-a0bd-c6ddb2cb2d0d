package com.trs.spark.cache.action;

import com.trs.common.datasource.SourceDriver;
import com.trs.spark.cache.CacheAction;
import com.trs.spark.configuration.Configurations;
import com.trs.spark.datasource.condition.SearchParams;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import static com.trs.spark.constant.CacheConstant.CACHE_HDFS_DEFAULT_PREFIX;
import static com.trs.spark.constant.CacheConstant.CACHE_HDFS_PREFIX;

/**
 * <AUTHOR>
 */
public class HdsfCacheAction implements CacheAction {

    private SourceDriver sourceDriver;

    private String pathPrefix;

    public HdsfCacheAction(SourceDriver sourceDriver) {
        this.sourceDriver = sourceDriver;
        try {
            pathPrefix = Configurations.getProperty(CACHE_HDFS_PREFIX).orElse(CACHE_HDFS_DEFAULT_PREFIX);
        } catch (NullPointerException e) {
            pathPrefix = CACHE_HDFS_DEFAULT_PREFIX;
        }
    }

    public HdsfCacheAction(SourceDriver sourceDriver, String pathPrefix) {
        this.sourceDriver = sourceDriver;
        this.pathPrefix = pathPrefix;
    }

    @Override
    public Dataset<Row> loadFromCache(SparkSession spark, String sourceTable, SearchParams params, String cacheKey) {
        return loadAndConvertFromCache(spark, sourceTable, params, cacheKey);
    }

    @Override
    public String cacheKey(String sourceTable, SearchParams param) {
        return buildCacheKey(sourceTable, param);
    }

    @Override
    public String cacheTable(String sourceTable, String cacheKey) {
        return new StringBuilder()
                .append(pathPrefix.startsWith("/") ? "" : "/")
                .append(pathPrefix)
                .append(pathPrefix.endsWith("/") ? "" : "/")
                .append(cacheKey)
                .append(".text")
                .toString();
    }

    @Override
    public SourceDriver sourceDriver() {
        return sourceDriver;
    }
}
