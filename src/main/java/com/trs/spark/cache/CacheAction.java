package com.trs.spark.cache;

import com.trs.common.datasource.SourceDriver;
import com.trs.spark.datasource.BaseSource;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.util.DigestUtils;
import com.trs.spark.util.SourceUtils;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public interface CacheAction extends Serializable {

    /**
     * 从缓存加载数据
     *
     * @param spark
     * @param sourceTable
     * @param params
     * @param cacheKey
     * @return null 缓存中没有数据 not null 从缓存中加载的数据
     */
    Dataset<Row> loadFromCache(SparkSession spark, String sourceTable, SearchParams params, String cacheKey);

    /**
     * 构建缓存的key
     * 比如：如果是hdfs就代表构建文件名字
     *
     * @param sourceTable
     * @param param
     * @return
     */
    String cacheKey(String sourceTable, SearchParams param);

    /**
     * 构建保存的表名
     * 比如 如果是hdfs就代表构建文件路径
     *
     * @param sourceTable 源数据的表名
     * @param cacheKey
     * @return
     */
    String cacheTable(String sourceTable, String cacheKey);

    /**
     * 获取source
     *
     * @return
     */
    SourceDriver sourceDriver();

    /**
     * 从缓存加载数据并且对字段类型转换
     *
     * @param spark
     * @param sourceTable
     * @param params
     * @param cacheKey
     * @return 结果
     */
    default Dataset<Row> loadAndConvertFromCache(SparkSession spark, String sourceTable, SearchParams params, String cacheKey) {
        try {
            String finalCacheKey = (null == cacheKey || cacheKey.isEmpty()) ? cacheKey(sourceTable, params) : cacheKey;
            String table = cacheTable(sourceTable, finalCacheKey);
            BaseSource baseSource = SourceUtils.makeSource(sourceDriver());
            Dataset<Row> dataset = baseSource.loadDataFromSource(spark, sourceDriver(), table);
            // 调用dataSet.isEmpty的成本是否很大？
            return dataset.isEmpty() ? null : dataset;
        } catch (Exception e) {
            spark.sparkContext().logWarning(() -> "从缓存加载数据失败(可能是没有此缓存文件)", e);
            return null;
        }
    }

    /**
     * 默认构造缓存key的方式
     *
     * @param sourceTable
     * @param params
     * @return
     */
    default String buildCacheKey(String sourceTable, SearchParams params) {
        String[] input = new String[]{sourceTable, params.getWhere(), params.getSql()};
        String finaleKey = Stream.of(input).map(String::valueOf).collect(Collectors.joining("-"));
        return DigestUtils.md5Hex(finaleKey);
    }

    /**
     * 默认执行缓存的方法
     *
     * @param spark
     * @param dataset
     * @param sourceTable
     * @param params
     * @param cacheKey
     */
    default void doCache(SparkSession spark, Dataset<Row> dataset, String sourceTable, SearchParams params, String cacheKey) {
        try {
            String key = (null == cacheKey || cacheKey.isEmpty()) ? cacheKey(sourceTable, params) : cacheKey;
            String table = cacheTable(sourceTable, key);
            BaseSource baseSource = SourceUtils.makeSource(sourceDriver());
            baseSource.writeDataToSource(spark, sourceDriver(), table, dataset, SaveMode.Overwrite);
        } catch (Exception e) {
            // 写入缓存失败……
            spark.sparkContext().logError(() -> "写入缓存异常", e);
        }
    }
}
