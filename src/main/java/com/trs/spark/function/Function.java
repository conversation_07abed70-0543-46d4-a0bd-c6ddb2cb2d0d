package com.trs.spark.function;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 支持序列化的函数接口
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 14:03
 * @version 1.0
 * @since 1.0
 */
@FunctionalInterface
public interface Function<T, R> extends Serializable, java.util.function.Function<T, R> {
}
