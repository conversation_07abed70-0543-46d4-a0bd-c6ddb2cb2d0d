package com.trs.spark.function;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * BiPredicate
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/8/16 14:39
 * @since 1.0
 */
@FunctionalInterface
public interface BiPredicate<T, U> extends Serializable, java.util.function.BiPredicate<T, U> {
}
