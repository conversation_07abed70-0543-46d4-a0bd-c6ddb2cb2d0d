package com.trs.spark.function;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 支持序列化的函数
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/21 16:52
 * @version 1.0
 * @since 1.0
 */
@FunctionalInterface
public interface Consumer<T> extends Serializable, java.util.function.Consumer<T> {
}
