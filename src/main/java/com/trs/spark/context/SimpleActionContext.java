package com.trs.spark.context;

import com.trs.spark.action.IAction;

import javax.annotation.Nonnull;
import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 12:47
 * @version 1.0
 * @since 1.0
 */
public class SimpleActionContext<OUT extends Serializable> implements IActionContext<OUT> {

    private IAction previous;

    private IAction next;

    private OUT executedResult;

    @Override
    public IAction prevAction() {
        return previous;
    }

    @Override
    public IAction nextAction() {
        return next;
    }

    @Override
    public void setPrevAction(@Nonnull IAction previous) {
        this.previous = previous;
    }

    @Override
    public void setNextAction(@Nonnull IAction next) {
        this.next = next;
    }

    @Override
    public OUT getExecutedResult() {
        return executedResult;
    }

    @Override
    public void setExecutedResult(OUT out) {
        this.executedResult = out;
    }
}
