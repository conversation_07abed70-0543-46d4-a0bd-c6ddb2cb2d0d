package com.trs.spark.context;

import com.trs.spark.action.IAction;

import javax.annotation.Nonnull;
import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 12:47
 * @version 1.0
 * @since 1.0
 */
public interface IActionContext<OUT extends Serializable> extends Serializable {

    /**
     * prevAction<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/6/14 18:51
     */
    IAction prevAction();

    /**
     * nextAction<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/6/14 18:51
     */
    IAction nextAction();

    /**
     * setPrevAction<BR>
     *
     * @param previous 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/6/14 18:51
     */
    void setPrevAction(@Nonnull IAction previous);

    /**
     * setNextAction<BR>
     *
     * @param next 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/6/14 18:51
     */
    void setNextAction(@Nonnull IAction next);

    /**
     * getExecutedResult<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/6/14 18:52
     */
    OUT getExecutedResult();

    /**
     * setExecutedResult<BR>
     *
     * @param out 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/6/14 18:52
     */
    void setExecutedResult(OUT out);
}
