package com.trs.spark.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * TimeCountVO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/5/30 19:07
 * @since 1.0
 */
@Data
public class CountAndTimeRangeVO<IN extends Serializable> extends BaseVO {

    private IN data;

    private Long count;

    private Date startTime;

    private Date endTime;

    public CountAndTimeRangeVO() {
        this(null, 0L, null, null);
    }

    public CountAndTimeRangeVO(IN data, Date time) {
        this(data, 1L, time, time);
    }

    public CountAndTimeRangeVO(IN data, Long count, Date startTime, Date endTime) {
        this.data = data;
        if (count == null) {
            this.count = 0L;
        } else {
            this.count = count;
        }
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
