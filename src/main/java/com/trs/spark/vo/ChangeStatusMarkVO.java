package com.trs.spark.vo;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * StatusChangeMarkVO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/6/14 18:32
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class ChangeStatusMarkVO extends StatusMarkVO {

    /**
     * 上一个状态
     */
    private String prevStatus;

    /**
     * 上一个状态的时间
     */
    private Date prevTime;

    public ChangeStatusMarkVO(String key, StatusMarkVO prev, StatusMarkVO now) {
        this.setKey(key);
        this.setPrevStatus(prev.getNowStatus());
        this.setPrevTime(prev.getNowTime());
        this.setNowStatus(now.getNowStatus());
        this.setNowTime(now.getNowTime());
    }
}
