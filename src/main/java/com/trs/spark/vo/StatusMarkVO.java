package com.trs.spark.vo;

import com.trs.common.utils.StringUtils;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * StatusChangeMarkVO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/6/14 18:32
 * @since 1.0
 */
@Data
public class StatusMarkVO extends BaseVO implements Comparable<StatusMarkVO> {

    /**
     * 数据对应的Key
     */
    private String key;

    /**
     * 当前状态
     */
    private String nowStatus;

    /**
     * 当前状态的时间
     */
    private Date nowTime;

    @Override
    public int compareTo(@NotNull StatusMarkVO o) {
        return getNowTime().compareTo(o.getNowTime());
    }

    public boolean isPresent() {
        if (StringUtils.isEmpty(getKey())) {
            return false;
        }
        if (StringUtils.isEmpty(getNowStatus())) {
            return false;
        }
        if (nowTime == null || nowTime.getTime() <= 0L) {
            return false;
        }
        return true;
    }

    public boolean isEmpty() {
        return !isPresent();
    }

    public static StatusMarkVO of(String key, String nowStatus, Date nowTime) {
        StatusMarkVO vo = new StatusMarkVO();
        vo.setKey(key);
        vo.setNowStatus(nowStatus);
        vo.setNowTime(nowTime);
        return vo;
    }
}
