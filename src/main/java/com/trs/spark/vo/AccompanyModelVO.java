package com.trs.spark.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * AccompanyModelVO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/8/16 14:14
 * @since 1.0
 */
@Data
@AllArgsConstructor
public class AccompanyModelVO<Key extends Serializable, IN extends IAccompanyModelIn>
        extends BaseVO {

    private Key key;

    private IN data;
}
