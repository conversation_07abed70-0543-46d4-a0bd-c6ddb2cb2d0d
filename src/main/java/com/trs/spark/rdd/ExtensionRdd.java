package com.trs.spark.rdd;

import com.trs.spark.function.CheckedFunction;
import com.trs.spark.function.Function;
import com.trs.spark.util.StreamUtils;
import io.vavr.Function2;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import scala.Tuple2;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * RDD操作相关工具类
 * *@author:wen.wen
 * *@create 2023-03-22 11:36
 **/
public class ExtensionRdd implements Serializable{

    /**
     * mapPartitions 操作
     *
     * @param rdd       rdd
     * @param convertor 转换器
     */
    public static <T, R> JavaRDD<R> optMap(JavaRDD<T> rdd, CheckedFunction<T, R> convertor) {
        return StreamUtils.mapPartitions(rdd, convertor);
    }

    /**
     * 将javaRDD转换成JavaPairRDD
     *
     * @param javaRdd         RDD
     * @param makeStandardKey key生成器
     * @return
     */
    public static <K extends Serializable, V> JavaPairRDD<K, V> mapPartitionsToPair(JavaRDD<V> javaRdd, Function<V, K> makeStandardKey) {
        return javaRdd.mapPartitionsToPair((iterator) -> {
            ArrayList<Tuple2<K, V>> result = new ArrayList<>();
            while (iterator.hasNext()) {
                V data = iterator.next();
                K key = makeStandardKey.apply(data);
                result.add(new Tuple2<>(key, data));
            }
            return result.iterator();
        });
    }

    /**
     * 执行 flatMapToPair 操作
     *
     * @param javaRdd         rdd
     * @param makeStandardKey key生成器
     */
    public static <K extends Serializable, V> JavaPairRDD<K, V> flatMapToPair(JavaRDD<V> javaRdd, Function<V, K> makeStandardKey) {
        return javaRdd.flatMapToPair((V item) -> {
            List<Tuple2<K, V>> data = new ArrayList<>();
            data.add(new Tuple2<>(makeStandardKey.apply(item), item));
            return data.iterator();
        });
    }

    /**
     * 执行 flatMapToPair 操作
     * 适用于一条数据对应多个key的场景
     *
     * @param javaRdd         rdd
     * @param makeStandardKey key生成器
     */
    public static <K extends Serializable, V> JavaPairRDD<K, V> flatMapToPairMutiKey(JavaRDD<V> javaRdd, Function<V, List<K>> makeStandardKey) {
        return javaRdd.flatMapToPair((V item) -> {
            List<Tuple2<K, V>> data = new ArrayList<>();
            makeStandardKey.apply(item).forEach((key) -> data.add(new Tuple2<>(key, item)));
            return data.iterator();
        });
    }

    /**
     * 优化的GroupBy操作,该操作主要通过aggraget来取代传统的GroupBy,如果rdd进行过Partition,那么效率将大大提高
     *
     * @param rdd             JavaRDD
     * @param makeStandardKey key生成器
     */
    public static <K extends Serializable, V> JavaPairRDD<K, List<V>> optGroupByWithCustomizeSeqOp(JavaRDD<V> rdd, Function<V, K> makeStandardKey) {
        Function2<List<V>, V, List<V>> f = (List<V> list, V one) -> {
            list.add(one);
            return list;
        };
        return optGroupByWithCustomizeSeqOp(rdd, makeStandardKey, f);
    }

    /**
     * 优化的GroupBy操作,该操作主要通过aggraget来取代传统的GroupBy,如果rdd进行过Partition,那么效率将大大提高
     *
     * @param rdd             JavaRDD
     * @param makeStandardKey key生成器
     * @param f               在SeqOp操作时能够进行干涉,及list添加单个元素时
     */
    public static <K extends Serializable, V> JavaPairRDD<K, List<V>> optGroupByWithCustomizeSeqOp(JavaRDD<V> rdd,
                                                                                                   Function<V, K> makeStandardKey,
                                                                                                   Function2<List<V>, V, List<V>> f) {
        return mapPartitionsToPair(rdd, makeStandardKey)
                .aggregateByKey(
                        new ArrayList<>(),
                        //单分区的操作行为
                        f::apply,
                        //不同分区相同key合并时的操作行为 list1:分区1得到的结果，list2:分区2得到的结果
                        (list1, list2) -> {
                            List<V> result = new ArrayList<>(list2);
                            for (V l : list1) {
                                result = f.apply(list2, l);
                            }
                            return result;
                        }
                );
    }
}
