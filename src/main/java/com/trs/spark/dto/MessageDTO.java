package com.trs.spark.dto;

import lombok.Data;

import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * kafka发送消息结构体
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/5/4 9:38
 * @version 1.0
 * @since 1.0
 */
@Data
public class MessageDTO {

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 消息ID用于追踪
     */
    private String serialId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    public static MessageDTO of(String messageType, String serialId, String content) {
        MessageDTO dto = new MessageDTO();
        dto.setMessageType(messageType);
        dto.setSerialId(serialId);
        dto.setCreateTime(new Date());
        dto.setContent(content);
        return dto;
    }

}
