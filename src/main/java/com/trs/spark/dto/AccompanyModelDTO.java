package com.trs.spark.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.spark.function.Function;
import com.trs.spark.function.IChecker;
import com.trs.spark.vo.IAccompanyModelIn;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/8/15 17:39
 * @since 1.0
 */
@Data
public class AccompanyModelDTO<Key extends Serializable, IN extends IAccompanyModelIn> extends BaseDTO {

    private Integer minDataSize;

    private Double threshold;

    private Function<IN, Key> makeKey;

    private List<Key> keys;

    private List<IChecker<IN>> checks;

    private Boolean hitOne;

    public AccompanyModelDTO(
            Integer minDataSize,
            Double threshold,
            Function<IN, Key> makeKey,
            List<Key> keys,
            List<IChecker<IN>> checks
    ) {
        this(minDataSize, threshold, makeKey, keys, checks, true);
    }

    public AccompanyModelDTO(
            Integer minDataSize,
            Double threshold,
            Function<IN, Key> makeKey,
            List<Key> keys,
            List<IChecker<IN>> checks,
            Boolean hitOne
    ) {
        this.minDataSize = minDataSize;
        this.threshold = threshold;
        this.makeKey = makeKey;
        this.keys = keys;
        this.checks = checks;
        this.hitOne = hitOne;
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotNull(getMinDataSize(), new ParamInvalidException("每个对象的最少数据量不能为空"));
        checkArgument(getMinDataSize() > 0L, new ParamInvalidException("每个对象的最少数据量不能为空"));
        checkNotNull(getThreshold(), new ParamInvalidException("阈值不能为空"));
        checkArgument(
                getThreshold() > 0.0 && getThreshold() <= 1.0,
                new ParamInvalidException("阈值区间为(0,1]")
        );
        checkNotNull(getMakeKey(), new ParamInvalidException("获取对象Key的方法不能为空"));
        checkArgument(CollectionUtils.isNotEmpty(getKeys()), new ParamInvalidException("代表性Key不能为空"));
        checkArgument(CollectionUtils.isNotEmpty(getChecks()), new ParamInvalidException("检测器不能为空"));
        return true;
    }
}
