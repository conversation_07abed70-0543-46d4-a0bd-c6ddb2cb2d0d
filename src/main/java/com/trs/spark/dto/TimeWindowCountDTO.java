package com.trs.spark.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * TimeWindowCountDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/6/2 11:20
 * @since 1.0
 */
@Data
public class TimeWindowCountDTO<IN extends Serializable> extends BaseDTO {

    private IN data;

    private String key;

    private Date time;

    public TimeWindowCountDTO(IN data, String key, Date time) {
        this.data = data;
        this.key = key;
        this.time = time;
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotNull(getData(), new ParamInvalidException("数据对象不能为空"));
        PreConditionCheck.checkNotEmpty(getKey(), new ParamInvalidException("数据对象Key不能为空"));
        PreConditionCheck.checkNotNull(getTime(), new ParamInvalidException("数据对象所属时间不能为空"));
        return true;
    }
}
