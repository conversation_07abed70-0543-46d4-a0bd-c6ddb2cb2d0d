package com.trs.spark.action;

import com.trs.spark.context.IActionContext;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 算子接口
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 11:52
 * @version 1.0
 * @since 1.0
 */
public interface IAction<IN extends Serializable, OUT extends Serializable> extends Serializable {

    /**
     * 获取当前上下文<BR>
     *
     * @return 上下文
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:20
     */
    IActionContext<OUT> getActionContext();

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param in    输入数据
     * @return 运算情况
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    Boolean action(SparkSession spark, IN in);

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param data  输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    OUT doAction(SparkSession spark, IN data);

    /**
     * 获取操作的执行结果<BR>
     *
     * @return 执行结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:32
     */
    OUT getExecutedResult();
}
