package com.trs.spark.action;

import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 合并From的计算
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 13:00
 * @version 1.0
 * @since 1.0
 */
public class MergeReadAction<R extends Serializable> extends BaseFromAction<JavaRDD<R>, JavaRDD<R>> {
    private AbstractNoInputFromAction<JavaRDD<R>> action;

    public MergeReadAction(AbstractNoInputFromAction<JavaRDD<R>> action) {
        this.action = action;
    }

    @Override
    public JavaRDD<R> doAction(SparkSession spark, JavaRDD<R> data) {
        JavaRDD<R> newJavaRDD = action.doAction(spark);
        return newJavaRDD !=null ? newJavaRDD.union(data) : data;
    }
}
