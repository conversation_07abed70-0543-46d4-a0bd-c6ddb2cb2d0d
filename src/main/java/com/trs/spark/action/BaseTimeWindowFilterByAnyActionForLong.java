package com.trs.spark.action;

import com.trs.spark.dto.TimeWindowCountDTO;
import com.trs.spark.function.Function;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseTimeWindowFilterByXActionForLong
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/5/30 19:06
 * @since 1.0
 */
public abstract class BaseTimeWindowFilterByAnyActionForLong<IN extends Serializable> extends BaseTimeWindowFilterByAnyAction<IN, Long> {

    public BaseTimeWindowFilterByAnyActionForLong(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan) {
        super(makeKey, makeTime, threshold, timeSpan);
    }

    public BaseTimeWindowFilterByAnyActionForLong(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan, Boolean distinct) {
        super(makeKey, makeTime, threshold, timeSpan, distinct);
    }

    /**
     * 构建初始化的Out对象<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/30 19:00
     */
    @Override
    public Long makeOutInitEntity() {
        return 0L;
    }

    /**
     * 检测输出的Out对象是否命中<BR>
     *
     * @param aLong     参数
     * @param threshold 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/30 19:02
     */
    @Override
    public Boolean checkOutIsHitThreshold(Long aLong, Integer threshold) {
        return aLong >= threshold.longValue();
    }

    /**
     * 获取对应时间跨度内最大长度<BR>
     *
     * @param list     时间列表
     * @param timeSpan 时间跨度
     * @param distinct 是否排重
     * @return 获取对应时间跨度内最大长度
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/17 11:38
     */
    @Override
    protected Long maxCount(List<TimeWindowCountDTO<IN>> list, Integer timeSpan, Boolean distinct) {
        return doMaxCount(
                list.stream().map(TimeWindowCountDTO::getTime).sorted().collect(Collectors.toList()),
                timeSpan,
                distinct
        );
    }

    /**
     * 获取对应时间跨度内最大长度<BR>
     *
     * @param date     时间列表
     * @param timeSpan 时间跨度
     * @param distinct 是否排重
     * @return 获取对应时间跨度内最大长度
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/17 11:38
     */
    protected abstract Long doMaxCount(List<Date> date, Integer timeSpan, Boolean distinct);
}
