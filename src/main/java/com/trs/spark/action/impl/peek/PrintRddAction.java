package com.trs.spark.action.impl.peek;

import com.trs.spark.function.Consumer;
import com.trs.spark.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.JavaRDD;

import java.io.Serializable;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * PrintRddAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:34
 * @since 1.0
 */
@Slf4j
public class PrintRddAction<IN extends Serializable>
        extends PrintAction<JavaRDD<IN>> {

    public PrintRddAction() {
        this(rdd -> "this rdd = " + rdd, log::info, true);
    }

    public PrintRddAction(boolean openPrint) {
        this(rdd -> "this rdd = " + rdd, log::info, openPrint);
    }

    public PrintRddAction(Function<List<IN>, String> makeOutInfo) {
        this(makeOutInfo, log::info, true);
    }

    public PrintRddAction(Function<List<IN>, String> makeOutInfo, boolean openPrint) {
        this(makeOutInfo, log::info, openPrint);
    }

    public PrintRddAction(Consumer<String> printInfo) {
        this(
                rdd -> "this rdd = " + rdd,
                printInfo,
                true
        );
    }

    public PrintRddAction(Consumer<String> printInfo, boolean openPrint) {
        this(
                rdd -> "this rdd = " + rdd,
                printInfo,
                openPrint
        );
    }

    public PrintRddAction(Function<List<IN>, String> makeOutInfo, Consumer<String> printInfo) {
        this(makeOutInfo, printInfo, true);
    }

    public PrintRddAction(Function<List<IN>, String> makeOutInfo, Consumer<String> printInfo, boolean openPrint) {
        super(rdd -> makeOutInfo.apply(rdd.collect()), printInfo, openPrint);
    }
}
