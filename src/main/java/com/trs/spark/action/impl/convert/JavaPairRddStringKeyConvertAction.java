package com.trs.spark.action.impl.convert;

import com.trs.spark.function.Function;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 转换成JavaPairRdd，Key的类型为String
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 17:58
 * @version 1.0
 * @since 1.0
 */
public class JavaPairRddStringKeyConvertAction<IN extends Serializable> extends JavaPairRddConvertAction<String, IN> {

    public JavaPairRddStringKeyConvertAction(Function<IN, String> function) {
        super(function);
    }

}
