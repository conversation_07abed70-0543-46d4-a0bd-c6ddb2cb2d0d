package com.trs.spark.action.impl.peek;

import com.trs.spark.function.Consumer;
import com.trs.spark.function.Function;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * PrintRddAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:34
 * @since 1.0
 */
@Slf4j
public class PrintAction<IN extends Serializable> extends BasePrintAction<IN> {

    public PrintAction() {
        this(
                info -> "this OutInfo = " + info,
                log::info,
                true
        );
    }

    public PrintAction(boolean openPrint) {
        this(
                info -> "this OutInfo = " + info,
                log::info,
                openPrint
        );
    }

    public PrintAction(Function<IN, String> makeOutInfo) {
        this(
                makeOutInfo,
                log::info,
                true
        );
    }


    public PrintAction(Function<IN, String> makeOutInfo, boolean openPrint) {
        this(
                makeOutInfo,
                log::info,
                openPrint
        );
    }

    public PrintAction(Consumer<String> printInfo) {
        this(
                info -> "this OutInfo = " + info,
                printInfo,
                true
        );
    }

    public PrintAction(Consumer<String> printInfo, boolean openPrint) {
        this(
                info -> "this OutInfo = " + info,
                printInfo,
                openPrint
        );
    }

    public PrintAction(Function<IN, String> makeOutInfo, Consumer<String> printInfo) {
        this(makeOutInfo, printInfo, true);
    }

    public PrintAction(Function<IN, String> makeOutInfo, Consumer<String> printInfo, boolean openPrint) {
        super(
                data -> printInfo.accept(makeOutInfo.apply(data)),
                openPrint
        );
    }
}
