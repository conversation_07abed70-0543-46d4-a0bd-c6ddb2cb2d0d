package com.trs.spark.action.impl.peek;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.spark.action.BasePeekAction;
import com.trs.spark.dto.MessageDTO;
import com.trs.spark.function.Function;
import com.trs.spark.util.KafkaSinkCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 向kafka写入数据得peek算子
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/4/25 17:23
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public class KafkaPeekAction<IN extends Serializable> extends BasePeekAction<JavaRDD<IN>> {

    private final String topic;

    private final String key;

    private final String messageType;

    private final Map<String, Object> configMap;

    private final Function<IN, String> convert;

    public KafkaPeekAction(String url, String topic, String messageType) {
        this(url, topic, null, messageType);
    }

    public KafkaPeekAction(String url, String topic, String key, String messageType) {
        this(new HashMap<>(3), topic, key, messageType, item -> {
            if (item instanceof String) {
                return (String) item;
            } else {
                return JSON.toJSONString(item);
            }
        });
        configMap.put("bootstrap.servers", url);
        configMap.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        configMap.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
    }

    public KafkaPeekAction(Map<String, Object> configMap, String topic, String messageType) {
        this(configMap, topic, null, messageType, item -> {
            if (item instanceof String) {
                return (String) item;
            } else {
                return JSON.toJSONString(item);
            }
        });
    }

    public KafkaPeekAction(Map<String, Object> configMap, String topic, String messageType, Function<IN, String> convert) {
        this(configMap, topic, null, messageType, convert);
    }

    public KafkaPeekAction(Map<String, Object> configMap, String topic, String key, String messageType) {
        this(configMap, topic, key, messageType, item -> {
            if (item instanceof String) {
                return (String) item;
            } else {
                return JSON.toJSONString(item);
            }
        });
    }

    public KafkaPeekAction(Map<String, Object> configMap, String topic, String key, String messageType, Function<IN, String> convert) {
        this.configMap = configMap;
        this.topic = topic;
        this.key = key;
        this.messageType = messageType;
        this.convert = convert;
    }


    @Override
    public void doPeek(SparkSession spark, JavaRDD<IN> javaRDD) {
        javaRDD.foreachPartition(itr -> {
            while (itr.hasNext()) {
                IN in = itr.next();
                String data = convert.apply(in);
                String serialId = String.valueOf(UUID.randomUUID());
                String message = JSONObject.toJSONString(MessageDTO.of(messageType, serialId, data));
                KafkaProducer<String, String> kafkaProducer = KafkaSinkCache.getProducer(configMap);
                log.info("kafka推送消息，configMap={},topic={},key={}，message={}", configMap, topic, key, message);
                ProducerRecord<String, String> record = new ProducerRecord<>(topic, key, message);
                kafkaProducer.send(record);
            }
        });
    }
}
