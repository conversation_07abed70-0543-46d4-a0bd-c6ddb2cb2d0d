package com.trs.spark.action.impl.from;

import com.trs.spark.datasource.HiveSource;
import com.trs.spark.function.Function;
import org.apache.spark.sql.Row;

import java.io.Serializable;

/**
 * 从海贝读取数据的操作行为
 *
 * @param <R>
 * <AUTHOR>
 */
public class HiveReadAction<R extends Serializable> extends BaseSourceReadAction<R> {

    public HiveReadAction(String table, Class<R> clazz) {
        this(table, null, null, null, clazz);
    }

    public HiveReadAction(String table, Function<Row, R> function) {
        this(table, null, null, null, function);
    }

    public HiveReadAction(String table, String where, String select, String sql, Class<R> clazz) {
        super(table, where, select, sql, clazz, new HiveSource(), null);
    }

    public HiveReadAction(String table, String where, String select, String sql, Function<Row, R> function) {
        super(table, where, select, sql, function, new HiveSource(), null);
    }

}
