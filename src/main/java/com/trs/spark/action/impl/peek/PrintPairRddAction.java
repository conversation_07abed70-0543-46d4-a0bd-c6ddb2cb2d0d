package com.trs.spark.action.impl.peek;

import com.trs.spark.function.Consumer;
import com.trs.spark.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.JavaPairRDD;
import scala.Tuple2;

import java.io.Serializable;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * PrintPairRddAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:34
 * @since 1.0
 */
@Slf4j
public class PrintPairRddAction<Key extends Serializable, IN extends Serializable>
        extends PrintAction<JavaPairRDD<Key, IN>> {

    public PrintPairRddAction() {
        this(rdd -> "this pairRdd = " + rdd, log::info, true);
    }

    public PrintPairRddAction(boolean openPrint) {
        this(rdd -> "this pairRdd = " + rdd, log::info, openPrint);
    }

    public PrintPairRddAction(Function<List<Tuple2<Key, IN>>, String> makeOutInfo) {
        this(makeOutInfo, log::info, true);
    }

    public PrintPairRddAction(Function<List<Tuple2<Key, IN>>, String> makeOutInfo, boolean openPrint) {
        this(makeOutInfo, log::info, openPrint);
    }

    public PrintPairRddAction(Consumer<String> printInfo) {
        this(
                rdd -> "this pairRdd = " + rdd,
                printInfo,
                true
        );
    }

    public PrintPairRddAction(Consumer<String> printInfo, boolean openPrint) {
        this(
                rdd -> "this pairRdd = " + rdd,
                printInfo,
                openPrint
        );
    }

    public PrintPairRddAction(Function<List<Tuple2<Key, IN>>, String> makeOutInfo, Consumer<String> printInfo) {
        this(makeOutInfo, printInfo, true);
    }

    public PrintPairRddAction(Function<List<Tuple2<Key, IN>>, String> makeOutInfo, Consumer<String> printInfo, boolean openPrint) {
        super(rdd -> makeOutInfo.apply(rdd.collect()), printInfo, openPrint);
    }
}
