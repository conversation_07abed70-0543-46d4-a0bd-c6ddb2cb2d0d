package com.trs.spark.action.impl.convert;

import com.trs.common.exception.ServiceException;
import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.dto.AccompanyModelDTO;
import com.trs.spark.function.IChecker;
import com.trs.spark.util.StreamUtils;
import com.trs.spark.vo.IAccompanyModelIn;
import com.trs.spark.vo.AccompanyModelVO;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/8/15 17:30
 * @since 1.0
 */
@Slf4j
public class AccompanyModelAction<Key extends Serializable, IN extends IAccompanyModelIn>
        extends BaseConvertAction<JavaRDD<IN>, JavaPairRDD<Key, ArrayList<Key>>> {

    private AccompanyModelDTO<Key, IN> dto;

    public AccompanyModelAction(@Nonnull AccompanyModelDTO<Key, IN> dto) {
        try {
            dto.isValid();
            this.dto = dto;
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param data  输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<Key, ArrayList<Key>> doAction(SparkSession spark, JavaRDD<IN> data) {
        var pairRdd = data.mapPartitionsToPair(
                items -> StreamUtils
                        .literators2Stream(items)
                        .map(item -> {
                            Key key = dto.getMakeKey().apply(item);
                            return new Tuple2<>(key, new AccompanyModelVO<>(key, item));
                        }).iterator()
        ).groupByKey().mapPartitionsToPair(
                items -> StreamUtils
                        .literators2Stream(items)
                        .map(item -> {
                            List<AccompanyModelVO<Key, IN>> list = new ArrayList<>(0);
                            item._2.forEach(list::add);
                            list.sort(Comparator.comparing(AccompanyModelVO::getData));
                            return new Tuple2<>(item._1(), list);
                        }).iterator()
        ).filter(item -> item._2.size() >= dto.getMinDataSize()).cache();
        var tmp = pairRdd.collect();
        var out = pairRdd.mapPartitionsToPair(
                items -> StreamUtils
                        .literators2Stream(items)
                        .filter(it -> dto.getKeys().contains(it._1))
                        .map(it -> {
                            ArrayList<Key> list = new ArrayList<>(0);
                            tmp.forEach(item -> {
                                if (!item._1.equals(it._1)) {
                                    if (checkIsHit(it._2, item._2, dto)) {
                                        list.add(item._1);
                                    }
                                }
                            });
                            return new Tuple2<>(it._1, list);
                        }).iterator()
        ).filter(item -> !item._2.isEmpty());
        pairRdd.unpersist();
        return out;
    }

    private boolean checkIsHit(List<AccompanyModelVO<Key, IN>> one, List<AccompanyModelVO<Key, IN>> two, AccompanyModelDTO dto) {
        int len1 = one.size();
        int len2 = two.size();
        int[][] c = new int[len1 + 1][len2 + 1];
        int[][] flag = new int[len1 + 1][len2 + 1];
        for (int i = 0; i < len1; i++) {
            for (int j = 0; j < len2; j++) {
                if (isNear(one.get(i).getData(), two.get(j).getData(), dto)) {
                    c[i + 1][j + 1] = c[i][j] + 1;
                    flag[i + 1][j + 1] = 0;
                } else if (c[i + 1][j] > c[i][j + 1]) {
                    c[i + 1][j + 1] = c[i + 1][j];
                    flag[i + 1][j + 1] = 1;
                } else {
                    c[i + 1][j + 1] = c[i][j + 1];
                    flag[i + 1][j + 1] = -1;
                }
            }
        }
        double size = lcss(flag, 0, len1, len2) * 1.0;
        double min = Math.min(len1, len2) * 1.0;
        double data = (size / min);
        log.info("[{}]跟[{}]的重合度为:{}", one.get(0).getKey(), two.get(0).getKey(), data);
        return dto.getThreshold() <= data;
    }

    private int lcss(int[][] flag, int count, int i, int j) {
        if (i * j == 0) {
            return count;
        } else if (flag[i][j] == 0) {
            count += 1;
            return lcss(flag, count, i - 1, j - 1);
        } else if (flag[i][j] == 1) {
            return lcss(flag, count, i, j - 1);
        } else {
            return lcss(flag, count, i - 1, j);
        }
    }

    private boolean isNear(IN one, IN two, AccompanyModelDTO dto) {
        List<IChecker<IN>> checks = dto.getChecks();
        if (dto.getHitOne()) {
            return checks.stream().anyMatch(check -> check.test(one, two));
        }
        return checks.stream().allMatch(check -> check.test(one, two));
    }
}
