package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.BiFunction;
import org.apache.commons.collections.CollectionUtils;
import org.apache.spark.HashPartitioner;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;
import scala.reflect.ClassTag;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * dbscan算法实现
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/21 16:42
 * @version 1.0
 * @since 1.0
 */
public class DBSCANAction<IN extends Serializable>
        extends BaseConvertAction<JavaRDD<IN>, JavaPairRDD<IN, ArrayList<IN>>> {

    /**
     * 密度半径
     */
    private Double r;

    /**
     * 簇中最少数量
     */
    private Long minPts;

    private BiFunction<IN, IN, Double> biFunction;

    private Integer partition_cnt;

    public DBSCANAction(Double r, Long minPts, BiFunction<IN, IN, Double> biFunction) {
        this(r, minPts, biFunction, 10);
    }

    public DBSCANAction(Double r, Long minPts, BiFunction<IN, IN, Double> biFunction, Integer partition_cnt) {
        this.r = r;
        this.minPts = minPts;
        this.biFunction = biFunction;
        this.partition_cnt = partition_cnt;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param rdd   输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<IN, ArrayList<IN>> doAction(SparkSession spark, JavaRDD<IN> rdd) {
        JavaPairRDD<Long, IN> dfinput = rdd.zipWithUniqueId().mapPartitionsToPair(items -> {
            ArrayList<Tuple2<Long, IN>> data = new ArrayList<>();
            items.forEachRemaining(item -> data.add(new Tuple2<>(item._2, item._1)));
            return data.iterator();
        });
        JavaPairRDD<Long, IN> rdd_input = dfinput.partitioner().isPresent() ? dfinput.cache() : dfinput
                .partitionBy(new HashPartitioner(20))
                .cache();
        JavaPairRDD<Long, IN> dfbuffer = dfinput.partitioner().isPresent() ? dfinput.cache() : dfinput
                .partitionBy(new HashPartitioner(partition_cnt))
                .cache();
        JavaPairRDD<Long, HashSet<Long>> rdd_core = makeCore(makeNear(spark, rdd_input, dfbuffer));
        for (int pcnt = partition_cnt; pcnt > 0; pcnt--) {
            rdd_core = mergeRdd(rdd_core, pcnt);
        }
        JavaPairRDD<Long, Long> dfcluster_ids = rdd_core.flatMapToPair(t -> t._2.stream()
                .map(i -> new Tuple2<>(i, t._1))
                .iterator());
        JavaPairRDD<Long, Tuple2<Long, IN>> rdd_cluster = dfinput.leftOuterJoin(dfcluster_ids).mapPartitionsToPair(items -> {
            ArrayList<Tuple2<Long, Tuple2<Long, IN>>> result = new ArrayList<>();
            items.forEachRemaining(item -> result.add(new Tuple2<>(item._2._2.get(), new Tuple2<>(item._1, item._2._1))));
            return result.iterator();
        });
        dfbuffer.unpersist();
        rdd_input.unpersist();
        return rdd_cluster.groupByKey()
                .mapPartitionsToPair(items -> {
                    ArrayList<Tuple2<IN, ArrayList<IN>>> result = new ArrayList<>();
                    items.forEachRemaining(item -> {
                        Long core_id = item._1;
                        IN core = null;
                        HashMap<Long, IN> map = new HashMap<>(0);
                        Iterable<Tuple2<Long, IN>> children_ids = item._2;
                        for (Tuple2<Long, IN> children : children_ids) {
                            if (children._1.equals(core_id)) {
                                core = children._2;
                            }
                            map.put(children._1, children._2);
                        }
                        ArrayList<IN> list = new ArrayList<>(map.values());
                        if (list.size() > 0) {
                            if (core == null) {
                                core = list.get(0);
                            }
                            result.add(new Tuple2<>(core, list));
                        }
                    });
                    return result.iterator();
                });
    }

    private JavaPairRDD<Long, Long> makeNear(SparkSession spark, JavaPairRDD<Long, IN> rdd_input, JavaPairRDD<Long, IN> dfbuffer) {
        JavaPairRDD<Long, Long> dfnear = spark.sparkContext()
                .emptyRDD(ClassTag.apply(JavaPairRDD.class))
                .toJavaRDD()
                .mapPartitionsToPair(items -> Collections.emptyIterator());
        for (int i = 0; i < partition_cnt; i++) {
            Integer partition_id = i;
            JavaPairRDD<Long, IN> bufferi = dfbuffer.mapPartitionsWithIndex((idx, iter) -> {
                        if (idx.equals(partition_id)) {
                            return iter;
                        } else {
                            return Collections.emptyIterator();
                        }
                    }, false)
                    .mapPartitionsToPair(items -> items);
            List<Tuple2<Long, IN>> list = bufferi.collect();
            Broadcast<List<Tuple2<Long, IN>>> tree_broads = spark.sparkContext().broadcast(list, ClassTag.apply(List.class));
            JavaPairRDD<Long, Long> dfneari = rdd_input.mapPartitionsToPair(iter -> {
                ArrayList<Tuple2<Long, Long>> res_list = new ArrayList<>();
                List<Tuple2<Long, IN>> tree = tree_broads.value();
                iter.forEachRemaining(cur -> tree.stream()
                        .filter(it -> biFunction.apply(it._2, cur._2) <= r)
                        .forEach(x -> res_list.add(new Tuple2<>(cur._1, x._1))));
                return res_list.iterator();
            });
            dfnear = dfnear.union(dfneari);
        }
        return dfnear;
    }

    private JavaPairRDD<Long, HashSet<Long>> makeCore(JavaPairRDD<Long, Long> dfnear) {
        JavaPairRDD<Long, HashSet<Long>> dfcore = dfnear.groupByKey().mapPartitionsToPair(items -> {
            ArrayList<Tuple2<Long, HashSet<Long>>> list = new ArrayList<>();
            items.forEachRemaining(it -> {
                HashSet<Long> ids = new HashSet<>();
                it._2.forEach(ids::add);
                list.add(new Tuple2<>(Collections.min(ids), ids));
            });
            return list.iterator();
        }).filter(r -> r._2.size() >= minPts);
        return dfcore;
    }

    public JavaPairRDD<Long, HashSet<Long>> mergeRdd(JavaPairRDD<Long, HashSet<Long>> rdd, Integer partition_cnt) {
        return rdd.partitionBy(new HashPartitioner(partition_cnt))
                .mapPartitionsToPair(items -> {
                    ArrayList<HashSet<Long>> buffer = new ArrayList<>();
                    items.forEachRemaining(it -> buffer.add(it._2));
                    ArrayList<HashSet<Long>> merged_buffer = mergerSets(buffer);
                    ArrayList<Tuple2<Long, HashSet<Long>>> result = new ArrayList<>();
                    for (HashSet<Long> core_id_set : merged_buffer) {
                        Long min_core_id = Collections.min(core_id_set);
                        result.add(new Tuple2<>(min_core_id, core_id_set));
                    }
                    return result.iterator();
                });
    }

    public ArrayList<HashSet<Long>> mergerSets(ArrayList<HashSet<Long>> in) {
        ArrayList<HashSet<Long>> result = new ArrayList<>();
        ArrayList<HashSet<Long>> set_list = (ArrayList<HashSet<Long>>) in.stream()
                .filter(i -> i != null && i.size() > 0)
                .collect(Collectors.toList());
        while (set_list.size() > 0) {
            HashSet<Long> cur_set = set_list.remove(0);
            HashSet<Integer> intersect_idxs = new HashSet<>(set_list.size());
            for (int i = 0; i < set_list.size(); i++) {
                if (CollectionUtils.intersection(cur_set, set_list.get(i)).size() > 0) {
                    cur_set.addAll(set_list.get(i));
                    intersect_idxs.add(i);
                }
            }
            for (Integer idx : intersect_idxs) {
                set_list.remove(idx);
            }
            result.add(cur_set);
        }
        return result;
    }
}
