package com.trs.spark.action.impl.convert;

import com.trs.spark.function.Predicate;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * javaPairRdd过滤
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 17:58
 * @version 1.0
 * @since 1.0
 */
public class JavaPairRddFilterOnlyV2Action<Key extends Serializable, IN extends Serializable>
        extends JavaPairRddFilterAction<Key, IN> {

    public JavaPairRddFilterOnlyV2Action(Predicate<IN> predicate) {
        super(item -> predicate.test(item._2()));
    }

}
