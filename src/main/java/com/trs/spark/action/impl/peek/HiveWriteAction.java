package com.trs.spark.action.impl.peek;

import com.trs.common.datasource.SourceDriver;
import com.trs.spark.datasource.BaseSource;
import com.trs.spark.datasource.HiveSource;
import com.trs.spark.function.CheckedFunction;
import org.apache.spark.sql.SaveMode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
public class HiveWriteAction<IN extends Serializable, Save extends Serializable> extends BaseSourceWriteAction<IN, Save>{

    public HiveWriteAction(String tableName, SaveMode saveMode, CheckedFunction<IN, Save> convertor, Class<Save> targetClass) {
        super(null, tableName, saveMode, convertor, targetClass);
    }

    @Override
    public BaseSource buildSource(SourceDriver sourceDriver) {
        return new HiveSource();
    }
}
