package com.trs.spark.action.impl.convert;

import scala.Tuple2;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * JavaPairRDD转JavaRDD
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/3/24 16:01
 * @version 1.0
 * @since 1.0
 */
public class JavaPairRddToJavaRddAction<Key extends Serializable, IN extends Serializable>
        extends JavaPairRddToJavaRddAndConvertAction<Key, IN, IN> {

    public JavaPairRddToJavaRddAction() {
        super(Tuple2::_2);
    }

}
