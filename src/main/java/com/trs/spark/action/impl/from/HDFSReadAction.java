package com.trs.spark.action.impl.from;

import com.trs.spark.function.Function;
import org.apache.spark.sql.Row;

import java.io.Serializable;

/**
 * 从hdfs读取数据的操作行为
 *
 * @param <R>
 * <AUTHOR>
 */
public class HDFSReadAction<R extends Serializable> extends BaseSourceReadAction<R> {


    public HDFSReadAction(String url, String table, String user, String password, Function<Row, R> function) {
        super(url, table, user, password, function);
    }

    public HDFSReadAction(String url, String table, String user, String password, String where, Function<Row, R> function) {
        super(url, table, user, password, where, function);
    }

    public HDFSReadAction(String url, String table, String user, String password, String where, String select, Function<Row, R> function) {
        super(url, table, user, password, where, select, function);
    }

    public HDFSReadAction(String url, String table, String user, String password, Class<R> clazz) {
        super(url, table, user, password, clazz);
    }

    public HDFSReadAction(String url, String table, String user, String password, String where, Class<R> clazz) {
        super(url, table, user, password, where, clazz);
    }

    public HDFSReadAction(String url, String table, String user, String password, String where, String select, Class<R> clazz) {
        super(url, table, user, password, where, select, clazz);
    }
}
