package com.trs.spark.action.impl.convert;

import com.trs.spark.function.Function;
import com.trs.spark.function.Predicate;
import lombok.Getter;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 11:55
 * @since 1.0
 */
public class JavaRddFilterAndConvertAction<IN extends Serializable, OUT extends Serializable>
        extends JavaRddConvertEntityAction<IN, OUT> {

    @Getter
    private Predicate<IN> predicateStart;

    public JavaRddFilterAndConvertAction(Predicate<IN> predicateStart, Function<IN, OUT> function) {
        this(predicateStart, function, i -> true);
    }

    public JavaRddFilterAndConvertAction(Predicate<IN> predicateStart, Function<IN, OUT> function, Predicate<OUT> predicateEnd) {
        super(function, predicateEnd);
        this.predicateStart = predicateStart;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param in    输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaRDD<OUT> doAction(SparkSession spark, JavaRDD<IN> in) {
        return super.doAction(spark, in.filter(item -> getPredicateStart().test(item)));
    }
}
