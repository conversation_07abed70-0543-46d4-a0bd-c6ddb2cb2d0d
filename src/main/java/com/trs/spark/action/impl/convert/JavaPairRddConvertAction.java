package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.Function;
import com.trs.spark.util.StreamUtils;
import lombok.Getter;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * JavaRdd转换成JavaPairRdd
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 17:58
 * @version 1.0
 * @since 1.0
 */
public class JavaPairRddConvertAction<Key extends Serializable, IN extends Serializable>
        extends BaseConvertAction<JavaRDD<IN>, JavaPairRDD<Key, IN>> {

    @Getter
    private Function<IN, Key> function;

    public JavaPairRddConvertAction(Function<IN, Key> function) {
        this.function = function;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark   SparkSession
     * @param javaRDD 输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<Key, IN> doAction(SparkSession spark, JavaRDD<IN> javaRDD) {
        return javaRDD.mapPartitionsToPair(items -> StreamUtils.literators2Stream(items)
                .map(item -> new Tuple2<>(getFunction().apply(item), item))
                .iterator()
        );
    }
}
