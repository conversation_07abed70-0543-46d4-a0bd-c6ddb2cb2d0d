package com.trs.spark.action.impl.from;

import com.trs.spark.function.Function;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;

import java.io.Serializable;
import java.util.HashMap;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件读取
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 13:01
 * @version 1.0
 * @since 1.0
 */
public class MultipleDataSourcesWithConvertValuesMapReadAction<R extends Serializable, OUT extends Serializable>
        extends MultipleDataSourcesMapReadAction<R, HashMap<String, OUT>> {

    public MultipleDataSourcesWithConvertValuesMapReadAction(HashMap<String, SparkProcessBuilder<JavaRDD<R>>> builders,
                                                             Function<JavaRDD<R>, OUT> function) {
        super(builders, map -> {
            HashMap<String, OUT> data = new HashMap<>(map.size());
            map.forEach((key, value) -> data.put(key, function.apply(value)));
            return data;
        });
    }

    @Deprecated
    public MultipleDataSourcesWithConvertValuesMapReadAction(HashMap<String, SparkProcessBuilder<JavaRDD<R>>> builders,
                                                             Class<R> clazz,
                                                             Function<JavaRDD<R>, OUT> function) {
        this(builders, function);
    }
}
