package com.trs.spark.action.impl.convert;

import com.trs.common.base.PreConditionCheck;
import com.trs.spark.action.BaseConvertAction;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.Optional;
import org.apache.spark.broadcast.Broadcast;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;
import scala.reflect.ClassTag;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 碰撞算子
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 20:49
 * @version 1.0
 * @since 1.0
 */
public class CollideAction<IN extends Serializable>
        extends BaseConvertAction<HashMap<String, JavaPairRDD<String, IN>>, JavaPairRDD<String, ArrayList<IN>>> {

    private Integer threshold;

    private String collisionExpression;

    public CollideAction(Integer threshold, String collisionExpression) {
        PreConditionCheck.checkArgument(threshold != null && threshold > 0, "阈值不能为空");
        PreConditionCheck.checkNotEmpty(collisionExpression, "条件表达式不能为空");
        this.threshold = threshold;
        this.collisionExpression = collisionExpression;
    }

    public Tuple2<List<String>, List<String>> getExpression() {
        List<String> valueKeys = Arrays.stream(collisionExpression.split(" "))
                .filter(i -> !"+-*()".contains(i))
                .collect(Collectors.toList());
        List<String> expressKeys = Arrays.stream(collisionExpression.split(" "))
                .filter("+-*"::contains)
                .collect(Collectors.toList());
        return new Tuple2<>(valueKeys, expressKeys);
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param in    输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<String, ArrayList<IN>> doAction(SparkSession spark, HashMap<String, JavaPairRDD<String, IN>> in) {
        Tuple2<List<String>, List<String>> express = getExpression();
        String oneKey = express._1.get(0);
        JavaPairRDD<String, IN> one = in.get(oneKey);
        List<String> otherData = express._1.subList(1, express._1.size());
        for (int i = 0; i < express._2.size(); i++) {
            one = doCount(one, in.get(otherData.get(i)), express._2.get(i));
        }
        Broadcast<Integer> sc = spark
                .sparkContext()
                .broadcast(threshold, ClassTag.apply(Integer.class));
        JavaPairRDD<String, ArrayList<IN>> result = one.groupByKey().mapPartitionsToPair(items -> {
            ArrayList<Tuple2<String, ArrayList<IN>>> data = new ArrayList<>();
            items.forEachRemaining(item -> {
                ArrayList<IN> list = new ArrayList<>();
                item._2.forEach(list::add);
                data.add(new Tuple2<>(item._1, list));
            });
            return data.iterator();
        }).filter(r -> r._2.size() >= sc.value());
        return result;
    }

    protected JavaPairRDD<String, IN> doCount(JavaPairRDD<String, IN> one, JavaPairRDD<String, IN> two, String flag) {
        switch (flag) {
            case "-":
                return subtract(one, two);
            case "--":
                return subtract(two, one);
            case "*":
                return intersection(one, two);
            case "+":
            default:
                return union(one, two);
        }
    }

    protected JavaPairRDD<String, IN> union(JavaPairRDD<String, IN> one, JavaPairRDD<String, IN> two) {
        return one.union(two);
    }

    protected JavaPairRDD<String, IN> subtract(JavaPairRDD<String, IN> one, JavaPairRDD<String, IN> two) {
        return one.leftOuterJoin(two)
                .filter(rdd -> !rdd._2._2.isPresent())
                .mapPartitionsToPair(items -> {
                    ArrayList<Tuple2<String, IN>> result = new ArrayList<>();
                    while (items.hasNext()) {
                        Tuple2<String, Tuple2<IN, Optional<IN>>> item = items.next();
                        result.add(new Tuple2<>(item._1, item._2._1));
                    }
                    return result.iterator();
                });
    }

    protected JavaPairRDD<String, IN> intersection(JavaPairRDD<String, IN> one, JavaPairRDD<String, IN> two) {
        JavaPairRDD<Tuple2<String, IN>, Tuple2<String, IN>> first = one.leftOuterJoin(two)
                .filter(i -> i._2._2.isPresent())
                .mapPartitionsToPair(items -> {
                    ArrayList<Tuple2<Tuple2<String, IN>, Tuple2<String, IN>>> result = new ArrayList<>();
                    while (items.hasNext()) {
                        Tuple2<String, Tuple2<IN, Optional<IN>>> item = items.next();
                        Tuple2<String, IN> t1 = new Tuple2<>(item._1, item._2._2.get());
                        Tuple2<String, IN> t2 = new Tuple2<>(item._1, item._2._1);
                        result.add(new Tuple2<>(t1, t2));
                    }
                    return result.iterator();
                });
        return first.mapPartitionsToPair(items -> {
            ArrayList<Tuple2<String, IN>> result = new ArrayList<>();
            while (items.hasNext()) {
                Tuple2<Tuple2<String, IN>, Tuple2<String, IN>> item = items.next();
                result.add(item._1);
            }
            return result.iterator();
        }).union(first.mapPartitionsToPair(items -> {
            ArrayList<Tuple2<String, IN>> result = new ArrayList<>();
            while (items.hasNext()) {
                Tuple2<Tuple2<String, IN>, Tuple2<String, IN>> item = items.next();
                result.add(item._2);
            }
            return result.iterator();
        }));
    }
}
