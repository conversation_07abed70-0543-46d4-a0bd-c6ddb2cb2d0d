package com.trs.spark.action.impl.peek;

import com.trs.spark.function.Consumer;
import com.trs.spark.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.JavaRDD;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * PrintRddCountAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:34
 * @since 1.0
 */
@Slf4j
public class PrintRddCountAction<IN extends Serializable>
        extends PrintAction<JavaRDD<IN>> {

    public PrintRddCountAction() {
        this(
                rdd -> "this rdd count = " + rdd,
                log::info,
                true
        );
    }

    public PrintRddCountAction(boolean openPrint) {
        this(
                rdd -> "this rdd count = " + rdd,
                log::info,
                openPrint
        );
    }

    public PrintRddCountAction(Function<Long, String> makeOutInfo) {
        this(
                makeOutInfo,
                log::info,
                true
        );
    }

    public PrintRddCountAction(Function<Long, String> makeOutInfo, boolean openPrint) {
        this(
                makeOutInfo,
                log::info,
                openPrint
        );
    }

    public PrintRddCountAction(Consumer<String> printInfo) {
        this(
                rdd -> "this rdd count = " + rdd,
                printInfo,
                true
        );
    }

    public PrintRddCountAction(Consumer<String> printInfo, boolean openPrint) {
        this(
                rdd -> "this rdd count = " + rdd,
                printInfo,
                openPrint
        );
    }

    public PrintRddCountAction(Function<Long, String> makeOutInfo, Consumer<String> printInfo) {
        this(makeOutInfo, printInfo, true);
    }

    public PrintRddCountAction(Function<Long, String> makeOutInfo, Consumer<String> printInfo, boolean openPrint) {
        super(rdd -> makeOutInfo.apply(rdd.count()), printInfo, openPrint);
    }
}
