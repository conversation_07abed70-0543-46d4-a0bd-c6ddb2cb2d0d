package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * convert过程中重新初始化RDD
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:19
 * @since 1.0
 */
public class JavaRddParallelizeAction<IN extends Serializable>
        extends BaseConvertAction<ArrayList<IN>, JavaRDD<IN>> {

    @Getter
    private Integer numSlices;

    public JavaRddParallelizeAction() {
        this(null);
    }

    public JavaRddParallelizeAction(Integer numSlices) {
        this.numSlices = numSlices;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param list  输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaRDD<IN> doAction(SparkSession spark, ArrayList<IN> list) {
        JavaSparkContext sparkContext = new JavaSparkContext(spark.sparkContext());
        Integer numSlices = getNumSlices() == null ? spark.sparkContext().defaultParallelism() : getNumSlices();
        if (CollectionUtils.isNotEmpty(list)) {
            return sparkContext.parallelize(list, numSlices);
        }
        return sparkContext.emptyRDD();
    }
}
