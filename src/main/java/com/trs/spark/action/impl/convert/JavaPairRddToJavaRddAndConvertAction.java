package com.trs.spark.action.impl.convert;

import com.trs.spark.function.Function;
import scala.Tuple2;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * JavaPairRDD转JavaRDD
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/3/24 16:01
 * @version 1.0
 * @since 1.0
 */
public class JavaPairRddToJavaRddAndConvertAction<Key extends Serializable, IN extends Serializable, OUT extends Serializable>
        extends JavaPairRddFilterAndConvertToJavaRddAction<Key, IN, OUT> {


    public JavaPairRddToJavaRddAndConvertAction(Function<Tuple2<Key, IN>, OUT> function) {
        super(i -> true, function);
    }
}
