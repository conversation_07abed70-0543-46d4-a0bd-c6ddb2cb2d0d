package com.trs.spark.action.impl.convert;

import com.trs.spark.function.Function;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 转换
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/3/24 16:01
 * @version 1.0
 * @since 1.0
 */
public class JavaRddToJavaPairRddStringKeyConvertAction<IN extends Serializable, OUT extends Serializable>
        extends JavaRddToJavaPairRddConvertAction<String, IN, OUT> {

    public JavaRddToJavaPairRddStringKeyConvertAction(Function<IN, String> keyFunction, Function<IN, OUT> outFunction) {
        super(keyFunction, outFunction);
    }
}
