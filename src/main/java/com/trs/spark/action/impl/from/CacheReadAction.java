package com.trs.spark.action.impl.from;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.CacheActionSourceFactory;
import com.trs.spark.action.AbstractNoInputFromAction;
import com.trs.spark.cache.CacheAction;
import com.trs.spark.cache.CacheCoder;
import com.trs.spark.cache.coder.JsonArrayCoder;
import com.trs.spark.configuration.Configurations;
import com.trs.spark.datasource.condition.SearchParams;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;
import java.util.Map;

import static com.trs.spark.constant.CacheConstant.*;

/**
 * <AUTHOR>
 */
public class CacheReadAction<R extends Serializable> extends AbstractNoInputFromAction<JavaRDD<R>> {

    /**
     * 从数据源加载数据的action
     */
    private BaseSourceReadAction<R> readAction;

    /**
     * 缓存操作的action
     */
    private CacheAction cacheAction;

    /**
     * 缓存数据的格式编解码器
     */
    private CacheCoder cacheCoder;

    /**
     * 从缓存加载数据时，字段类型映射
     */
    private Map<String, String> fileTypeMapping;

    /**
     * 公因条件
     */
    private String commonWhereCondition;

    public CacheReadAction(BaseSourceReadAction<R> readAction) {
        this.readAction = readAction;
        SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver(
                Configurations.getProperty(CACHE_URL).get(),
                Configurations.getProperty(CACHE_USERNAME).orElse(""),
                Configurations.getProperty(CACHE_PASSWORD).orElse("")
        ).get();
        cacheAction = CacheActionSourceFactory.actionFromDriver(sourceDriver);
        cacheCoder = new JsonArrayCoder();
    }

    public CacheReadAction(BaseSourceReadAction<R> readAction, CacheAction cacheAction, CacheCoder cacheCoder, Map<String, String> fileTypeMapping, String commonWhereCondition) {
        this.readAction = readAction;
        this.cacheAction = cacheAction;
        this.cacheCoder = cacheCoder;
        this.fileTypeMapping = fileTypeMapping;
        this.commonWhereCondition = commonWhereCondition;
    }

    @Override
    public JavaRDD<R> doAction(SparkSession spark) {
        // 读取数据
        Dataset<Row> rowDataset = tryLoadFromCacheOrElseFromSource(spark);
        // 过滤数据
        Dataset<Row> filtered = dataFilter(spark, rowDataset);
        // 转换成javaRdd
        JavaRDD<R> rdd = readAction.toJavaRdd(spark, filtered);
        return rdd;
    }

    /**
     * 尝试从缓存中捞取数据，如果缓存中没有捞取到数据，就从数据源捞取数据
     * @return
     */
    private Dataset<Row> tryLoadFromCacheOrElseFromSource(SparkSession spark) {
        spark.sparkContext().logInfo(() -> "尝试从缓存读取数据");
        Dataset<Row> rowDataset = loadFromCacheOrNull(spark);
        if (null != rowDataset) {
            spark.sparkContext().logInfo(() -> "从缓存读取到数据成功");
            return rowDataset;
        } else {
            spark.sparkContext().logInfo(() -> "从缓存读取到的数据为空");
        }

        spark.sparkContext().logInfo(() -> "从数据源加载数据");
        Dataset<Row> rowDatasetFromSource = loadFromSource(spark);
        // 写入缓存
        writeToCache(spark, rowDatasetFromSource);
        spark.sparkContext().logInfo(() -> "从数据源加载数据成功");
        return rowDatasetFromSource;
    }

    /**
     * 仅从缓存中捞取数据
     * 如果缓存中没有或者异常时，返回null
     * @param spark
     * @return
     */
    private Dataset<Row> loadFromCacheOrNull(SparkSession spark) {
        // 如果禁用了缓存 还是走原来的加载方式
        Boolean enable = Boolean.valueOf(Configurations.getProperty(CACHE_ENABLE).orElse("true"));
        if (!enable) {
            spark.sparkContext().logInfo(() -> "禁用了缓存，跳过从缓存加载数据");
            return null;
        }
        // 查找缓存
        SearchParams params = readAction.searchParams();
        String sourceTable = readAction.getTable();
        Dataset<Row> cachedDataSet = cacheAction.loadFromCache(spark, sourceTable, params, getCommonConditionCacheKey());
        if (null == cachedDataSet) {
            return null;
        }
        // 解析缓存内容
        Dataset<Row> dataset = cacheCoder.deCode(spark, cachedDataSet);
        // 字段类型转换
        if (null != fileTypeMapping && !fileTypeMapping.isEmpty()) {
            for (Map.Entry<String, String> entry : fileTypeMapping.entrySet()) {
                String name = entry.getKey();
                String type = entry.getValue();
                dataset = dataset.withColumn(name, dataset.col(name).cast(type));
            }
        }
        return dataset;
    }

    /**
     * 从数据源捞取数据
     * @param spark
     * @return
     */
    private Dataset<Row> loadFromSource(SparkSession spark) {
        if (null == commonWhereCondition || commonWhereCondition.isEmpty()) {
            spark.sparkContext().logInfo(() -> "从源数据源加载数据（没有公因条件）");
            Dataset<Row> dataSet = readAction.getDataSet(spark);
            spark.sparkContext().logInfo(() -> "从源数据源加载数据成功（没有公因条件）");
            return dataSet;
        }
        spark.sparkContext().logInfo(() -> "从源数据源加载数据（有公因条件）");
        SearchParams searchParams = readAction.searchParams();
        SearchParams commonSearchParams = new SearchParams();
        commonSearchParams.setWhere(commonWhereCondition);
        commonSearchParams.setSelectFields(searchParams.getSelectFields());
        Dataset<Row> dataSet = readAction.getDataSet(spark, commonSearchParams);
        spark.sparkContext().logInfo(() -> "从源数据源加载数据成功（有公因条件）");
        return dataSet;
    }


    /**
     * 写入缓存
     * @param spark
     * @param dataSet
     */
    private void writeToCache(SparkSession spark, Dataset<Row> dataSet) {
        // 写入缓存
        try {
            spark.sparkContext().logInfo(() -> "开始写入缓存");
            // 编码成缓存的内容
            Dataset dataset = cacheCoder.enCode(spark, dataSet);
            // 执行缓存
            cacheAction.doCache(spark, dataset, readAction.getTable(), readAction.searchParams(), getCommonConditionCacheKey());
            spark.sparkContext().logInfo(() -> "写入缓存成功");
        } catch (Exception e) {
            // 写入缓存出错……
            spark.sparkContext().logError(() -> "写入缓存失败", e);
        }
    }

    /**
     * 根据公因条件生成缓存的key
     * @return
     */
    private String getCommonConditionCacheKey() {
        SearchParams commonParams = new SearchParams();
        commonParams.setWhere(commonWhereCondition);
        String cacheKey = (null == commonWhereCondition || commonWhereCondition.isEmpty())
                ? null
                : cacheAction.buildCacheKey(readAction.getTable(), commonParams);
        return cacheKey;
    }

    /**
     * @param dataset
     * @return
     */
    private Dataset<Row> dataFilter(SparkSession spark, Dataset<Row> dataset) {
        // 没有公因条件 说明直接从数据源读取的数据 无需过滤
        if (null == commonWhereCondition || commonWhereCondition.isEmpty()) {
            return dataset;
        }
        // 有公因条件 还要根据数据源的过滤条件过滤
        String where = readAction.searchParams().getWhere();
        if (null == where || where.isEmpty()) {
            spark.logWarning(() -> "未对公因条件捞取出来的数据做过滤");
            return dataset;
        }
        Dataset<Row> filtered = dataset.where(where);
        return filtered;
    }
}