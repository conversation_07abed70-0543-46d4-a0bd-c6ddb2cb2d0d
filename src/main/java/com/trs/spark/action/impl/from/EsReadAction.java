package com.trs.spark.action.impl.from;

import com.trs.common.datasource.SourceDriver;
import com.trs.spark.datasource.BaseSource;
import com.trs.spark.datasource.EsSource;
import com.trs.spark.function.Function;
import org.apache.spark.sql.Row;

import java.io.Serializable;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：从ES读取数据的操作行为
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/11/15 14:05
 * @since 1.0
 */
public class EsReadAction<R extends Serializable> extends BaseSourceReadAction<R> {


    public EsReadAction(String url, String table, String user, String password, Function<Row, R> function) {
        super(url, table, user, password, function);
    }

    public EsReadAction(String url, String table, String user, String password, String where, Function<Row, R> function) {
        super(url, table, user, password, where, function);
    }

    public EsReadAction(String url, String table, String user, String password, String where, String select, Function<Row, R> function) {
        super(url, table, user, password, where, select, function);
    }

    public EsReadAction(String url, String table, String user, String password, Class<R> clazz) {
        super(url, table, user, password, clazz);
    }

    public EsReadAction(String url, String table, String user, String password, String where, Class<R> clazz) {
        super(url, table, user, password, where, clazz);
    }

    public EsReadAction(String url, String table, String user, String password, String where, String select, Class<R> clazz) {
        super(url, table, user, password, where, select, clazz);
    }

    @Override
    public BaseSource baseSource(SourceDriver sourceDriver) {
        return new EsSource();
    }
}
