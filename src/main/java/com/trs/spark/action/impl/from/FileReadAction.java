package com.trs.spark.action.impl.from;

import com.trs.spark.action.AbstractNoInputFromAction;
import com.trs.spark.function.Function;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件读取
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 13:01
 * @version 1.0
 * @since 1.0
 */
public class FileReadAction<R extends Serializable> extends AbstractNoInputFromAction<JavaRDD<R>> {

    private String filePath;

    private Function<String, R> function;

    public FileReadAction(String filePath) {
        this(filePath, a -> (R) a);
    }

    public FileReadAction(String filePath, Function<String, R> function) {
        this.filePath = filePath;
        this.function = function;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaRDD<R> doAction(SparkSession spark) {
        return spark
                .read()
                .textFile(filePath)
                .javaRDD()
                .map(item -> function.apply(item));
    }
}
