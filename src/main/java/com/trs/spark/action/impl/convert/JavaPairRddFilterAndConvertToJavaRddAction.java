package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.Function;
import com.trs.spark.function.Predicate;
import com.trs.spark.util.StreamUtils;
import lombok.Getter;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * JavaPairRDD转JavaRDD
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/3/24 16:01
 * @version 1.0
 * @since 1.0
 */
public class JavaPairRddFilterAndConvertToJavaRddAction<Key extends Serializable, IN extends Serializable, OUT extends Serializable>
        extends BaseConvertAction<JavaPairRDD<Key, IN>, JavaRDD<OUT>> {

    @Getter
    private Function<Tuple2<Key, IN>, OUT> function;

    @Getter
    private Predicate<Tuple2<Key, IN>> predicate;

    public JavaPairRddFilterAndConvertToJavaRddAction(Predicate<Tuple2<Key, IN>> predicate, Function<Tuple2<Key, IN>, OUT> function) {
        this.predicate = predicate;
        this.function = function;
    }

    @Override
    public JavaRDD<OUT> doAction(SparkSession spark, JavaPairRDD<Key, IN> in) {
        return in.filter(i -> getPredicate().test(i))
                .mapPartitions(items -> StreamUtils.literators2Stream(items)
                        .map(item -> getFunction().apply(item))
                        .iterator()
                );
    }
}
