package com.trs.spark.action.impl.peek;

import com.trs.spark.function.Consumer;
import com.trs.spark.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.JavaRDD;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * PrintRddAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:34
 * @since 1.0
 */
@Slf4j
public class PrintRddItemAction<IN extends Serializable> extends BasePrintAction<JavaRDD<IN>> {

    public PrintRddItemAction() {
        this(
                info -> "this rddItem = " + info,
                log::info,
                true
        );
    }

    public PrintRddItemAction(boolean openPrint) {
        this(
                info -> "this rddItem = " + info,
                log::info,
                openPrint
        );
    }

    public PrintRddItemAction(Function<IN, String> makeOutInfo) {
        this(
                makeOutInfo,
                log::info,
                true
        );
    }


    public PrintRddItemAction(Function<IN, String> makeOutInfo, boolean openPrint) {
        this(
                makeOutInfo,
                log::info,
                openPrint
        );
    }

    public PrintRddItemAction(Consumer<String> printInfo) {
        this(
                info -> "this rddItem = " + info,
                printInfo,
                true
        );
    }

    public PrintRddItemAction(Consumer<String> printInfo, boolean openPrint) {
        this(
                info -> "this rddItem = " + info,
                printInfo,
                openPrint
        );
    }

    public PrintRddItemAction(Function<IN, String> makeOutInfo, Consumer<String> printInfo) {
        this(makeOutInfo, printInfo, true);
    }

    public PrintRddItemAction(Function<IN, String> makeOutInfo, Consumer<String> printInfo, boolean openPrint) {
        super(
                data -> data.foreachPartition(items -> items.forEachRemaining(item -> printInfo
                        .accept(makeOutInfo.apply(item)))),
                openPrint
        );
    }
}
