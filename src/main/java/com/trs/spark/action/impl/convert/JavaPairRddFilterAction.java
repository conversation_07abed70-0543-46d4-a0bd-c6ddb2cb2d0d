package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.Predicate;
import lombok.Getter;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * javaPairRdd过滤
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 17:58
 * @version 1.0
 * @since 1.0
 */
public class JavaPairRddFilterAction<Key extends Serializable, IN extends Serializable>
        extends BaseConvertAction<JavaPairRDD<Key, IN>, JavaPairRDD<Key, IN>> {

    @Getter
    private Predicate<Tuple2<Key, IN>> predicate;

    public JavaPairRddFilterAction(Predicate<Tuple2<Key, IN>> predicate) {
        this.predicate = predicate;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark   SparkSession
     * @param javaRDD 输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<Key, IN> doAction(SparkSession spark, JavaPairRDD<Key, IN> javaRDD) {
        return javaRDD.filter(item -> getPredicate().test(item));
    }
}
