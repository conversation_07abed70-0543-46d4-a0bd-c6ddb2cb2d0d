package com.trs.spark.action.impl.from;

import com.trs.common.base.PreConditionCheck;
import com.trs.spark.action.AbstractNoInputFromAction;
import com.trs.spark.function.Function;
import com.trs.spark.process.SparkProcessBuilder;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;
import java.util.HashMap;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件读取
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 13:01
 * @version 1.0
 * @since 1.0
 */
public class MultipleDataSourcesMapReadAction<R extends Serializable, OUT extends Serializable> extends AbstractNoInputFromAction<OUT> {

    private HashMap<String, SparkProcessBuilder<JavaRDD<R>>> builders;

    private Function<HashMap<String, JavaRDD<R>>, OUT> function;

    public MultipleDataSourcesMapReadAction(HashMap<String, SparkProcessBuilder<JavaRDD<R>>> builders) {
        this(builders, map -> (OUT) map);
    }

    @Deprecated
    public MultipleDataSourcesMapReadAction(
            HashMap<String, SparkProcessBuilder<JavaRDD<R>>> builders,
            Class<R> clazz) {
        this(builders, map -> (OUT) map);
    }

    public MultipleDataSourcesMapReadAction(
            HashMap<String, SparkProcessBuilder<JavaRDD<R>>> builders,
            Function<HashMap<String, JavaRDD<R>>, OUT> function) {
        PreConditionCheck.checkArgument(builders != null && builders.size() > 0, "流程构造器不能为空");
        this.builders = builders;
        this.function = function;
    }

    @Deprecated
    public MultipleDataSourcesMapReadAction(
            HashMap<String, SparkProcessBuilder<JavaRDD<R>>> builders,
            Class<R> clazz,
            Function<HashMap<String, JavaRDD<R>>, OUT> function) {
        this(builders, function);
    }


    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public OUT doAction(SparkSession spark) {
        HashMap<String, JavaRDD<R>> map = new HashMap<>(builders.size());
        for (String key : builders.keySet()) {
            SparkProcessBuilder<JavaRDD<R>> builder = builders.get(key);
            map.put(key, builder.doRun(spark));
        }
        return function.apply(map);
    }
}
