package com.trs.spark.action.impl.from;

import com.trs.spark.action.AbstractNoInputFromAction;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * from算子-初始化RDD
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:25
 * @since 1.0
 */
public class JavaRddParallelizeFromAction<R extends Serializable> extends AbstractNoInputFromAction<JavaRDD<R>> {

    @Getter
    private List<R> data;

    @Getter
    private Integer numSlices;

    public JavaRddParallelizeFromAction(List<R> data) {
        this(data, null);
    }

    public JavaRddParallelizeFromAction(List<R> data, Integer numSlices) {
        this.data = data;
        this.numSlices = numSlices;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaRDD<R> doAction(SparkSession spark) {
        JavaSparkContext sparkContext = new JavaSparkContext(spark.sparkContext());
        Integer numSlices = getNumSlices() == null ? spark.sparkContext().defaultParallelism() : getNumSlices();
        if (CollectionUtils.isNotEmpty(data)) {
            return sparkContext.parallelize(getData(), numSlices);
        }
        return sparkContext.emptyRDD();
    }
}
