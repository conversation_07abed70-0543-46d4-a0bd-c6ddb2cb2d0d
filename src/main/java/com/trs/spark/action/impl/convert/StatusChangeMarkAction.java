package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.BiFunction;
import com.trs.spark.function.Function;
import com.trs.spark.util.StreamUtils;
import com.trs.spark.vo.ChangeStatusMarkVO;
import com.trs.spark.vo.StatusMarkVO;
import lombok.Builder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 状态变迁标记算子
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/6/14 18:26
 * @since 1.0
 */
@Builder
public class StatusChangeMarkAction<IN extends Serializable> extends BaseConvertAction<JavaRDD<IN>, JavaPairRDD<String, ArrayList<ChangeStatusMarkVO>>> {

    private Function<IN, String> makeKey;

    private Function<IN, Date> makeTime;

    private Function<IN, String> markStatus;

    /**
     * 检查两个数据之间的时间是否合适
     */
    private BiFunction<Date, Date, Boolean> checkTime;

    /**
     * 检查两个数据之间的时间是否超时
     */
    private BiFunction<Date, Date, Boolean> dataOutTime;

    /**
     * 是否按照超过平均时间间隔标记
     */
    private Boolean markByExAveTime;

    /**
     * 开启平均时间之后最小时间间隔（单位毫秒）
     */
    private Long minAveTime;

    public StatusChangeMarkAction(
            Function<IN, String> makeKey,
            Function<IN, Date> makeTime,
            Function<IN, String> markStatus
    ) {
        this(makeKey, makeTime, markStatus, (a, b) -> true, (a, b) -> false, false, 0L);
    }

    public StatusChangeMarkAction(
            Function<IN, String> makeKey,
            Function<IN, Date> makeTime,
            Function<IN, String> markStatus,
            Long minAveTime
    ) {
        this(makeKey, makeTime, markStatus, (a, b) -> true, (a, b) -> false, false, minAveTime);
    }

    public StatusChangeMarkAction(
            Function<IN, String> makeKey,
            Function<IN, Date> makeTime,
            Function<IN, String> markStatus,
            Boolean markByExAveTime
    ) {
        this(makeKey, makeTime, markStatus, (a, b) -> true, (a, b) -> false, markByExAveTime, 0L);
    }

    public StatusChangeMarkAction(
            Function<IN, String> makeKey,
            Function<IN, Date> makeTime,
            Function<IN, String> markStatus,
            Boolean markByExAveTime,
            Long minAveTime
    ) {
        this(makeKey, makeTime, markStatus, (a, b) -> true, (a, b) -> false, markByExAveTime, minAveTime);
    }

    public StatusChangeMarkAction(Function<IN, String> makeKey,
                                  Function<IN, Date> makeTime,
                                  Function<IN, String> markStatus,
                                  BiFunction<Date, Date, Boolean> checkTime) {
        this(makeKey, makeTime, markStatus, checkTime, (a, b) -> false, false, 0L);
    }

    public StatusChangeMarkAction(
            Function<IN, String> makeKey,
            Function<IN, Date> makeTime,
            Function<IN, String> markStatus,
            BiFunction<Date, Date, Boolean> checkTime,
            BiFunction<Date, Date, Boolean> dataOutTime,
            Boolean markByExAveTime
    ) {
        this(makeKey, makeTime, markStatus, checkTime, dataOutTime, markByExAveTime, 0L);
    }

    public StatusChangeMarkAction(
            Function<IN, String> makeKey,
            Function<IN, Date> makeTime,
            Function<IN, String> markStatus,
            BiFunction<Date, Date, Boolean> checkTime,
            BiFunction<Date, Date, Boolean> dataOutTime,
            Boolean markByExAveTime,
            Long minAveTime
    ) {
        this.makeKey = makeKey;
        this.makeTime = makeTime;
        this.markStatus = markStatus;
        this.checkTime = checkTime;
        this.dataOutTime = dataOutTime;
        this.markByExAveTime = markByExAveTime;
        this.minAveTime = minAveTime;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param data  输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<String, ArrayList<ChangeStatusMarkVO>> doAction(SparkSession spark, JavaRDD<IN> data) {
        return data.mapPartitionsToPair(items -> StreamUtils.literators2Stream(items)
                        .map(this::markStatus)
                        .filter(r -> r._2.isPresent())
                        .iterator()
                ).groupByKey()
                .mapPartitionsToPair(items -> StreamUtils.literators2Stream(items)
                        .map(this::reduceStatusMark)
                        .filter(r -> CollectionUtils.isNotEmpty(r._2))
                        .iterator());
    }

    private Tuple2<String, StatusMarkVO> markStatus(IN data) {
        String key = makeKey.apply(data);
        Date time = makeTime.apply(data);
        String status = markStatus.apply(data);
        return new Tuple2<>(key, StatusMarkVO.of(key, status, time));
    }

    private Tuple2<String, ArrayList<ChangeStatusMarkVO>> reduceStatusMark(Tuple2<String, Iterable<StatusMarkVO>> data) {
        List<StatusMarkVO> list = StreamUtils.literators2Stream(data._2.iterator())
                .sorted()
                .collect(Collectors.toList());
        String key = data._1;
        if (list.isEmpty() || list.size() == 1) {
            return new Tuple2<>(key, new ArrayList<>(0));
        }
        final Long ave;
        int len = list.size();
        if (markByExAveTime) {
            Long diff = 0L;
            for (int i = 1; i < len; i++) {
                StatusMarkVO prev = list.get(i - 1);
                StatusMarkVO now = list.get(i);
                diff += Math.abs(now.getNowTime().getTime() - prev.getNowTime().getTime());
            }
            ave = Math.max(diff / (len - 1), minAveTime);
        } else {
            ave = Long.MAX_VALUE;
        }
        ArrayList<ChangeStatusMarkVO> result = new ArrayList<>(len - 1);
        for (int i = 1; i < len; i++) {
            StatusMarkVO prev = list.get(i - 1);
            StatusMarkVO now = list.get(i);
            Long timeDiff = Math.abs(prev.getNowTime().getTime() - now.getNowTime().getTime());
            if (checkTime.apply(prev.getNowTime(), now.getNowTime())) {
                if (!prev.getNowStatus().equals(now.getNowStatus())
                        || dataOutTime.apply(prev.getNowTime(), now.getNowTime())) {
                    result.add(new ChangeStatusMarkVO(key, prev, now));
                } else if (markByExAveTime && len > 2 && (timeDiff) > ave) {
                    result.add(new ChangeStatusMarkVO(key, prev, now));
                }
            }
        }
        return new Tuple2<>(key, result);
    }
}
