package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.Function;
import com.trs.spark.function.Predicate;
import com.trs.spark.util.StreamUtils;
import lombok.Getter;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * JavaRDD转换
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/3/24 16:01
 * @version 1.0
 * @since 1.0
 */
public class JavaRddConvertEntityAction<IN extends Serializable, OUT extends Serializable>
        extends BaseConvertAction<JavaRDD<IN>, JavaRDD<OUT>> {

    @Getter
    private Function<IN, OUT> function;

    @Getter
    private Predicate<OUT> predicateEnd;

    public JavaRddConvertEntityAction(Function<IN, OUT> function) {
        this(function, i -> true);
    }

    public JavaRddConvertEntityAction(Function<IN, OUT> function, Predicate<OUT> predicateEnd) {
        this.function = function;
        this.predicateEnd = predicateEnd;
    }

    @Override
    public JavaRDD<OUT> doAction(SparkSession spark, JavaRDD<IN> in) {
        return in.mapPartitions(items -> StreamUtils.literators2Stream(items)
                .map(item -> getFunction().apply(item))
                .iterator()
        ).filter(i -> getPredicateEnd().test(i));
    }
}
