package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.function.FilterFunction;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.expressions.Window;
import org.apache.spark.sql.expressions.WindowSpec;

import java.io.Serializable;
import java.util.Objects;

import static org.apache.spark.sql.functions.*;

/**
 * 逗留点模型
 * <AUTHOR>
 * @date 2023/4/3
 */
public class StayAction<IN extends Serializable> extends BaseConvertAction<JavaRDD<IN>,  JavaRDD<IN>> {
    /**
     * 百分位数
     */
    private Double percentile = Double.valueOf(System.getProperty("action.stay.percentile", "0.7"));

    /**
     * 阀值
     */
    private Integer threshold = 0;

    /**
     * 对象ID字段
     */
    private String objectId;

    /**
     * 地点标识
     */
    private String place;

    /**
     * 地点字段
     */
    private String location;

    /**
     * 地球经度
     */
    private String jdwgs84;

    /**
     * 地球维度
     */
    private String wdwgs84;

    /**
     * 地点到达时间字段
     */
    private String time;

    private Class<IN> targetClass;

    /**
     * 下个地点标识字段
     */
    private String nextPlace = "nextPlace";

    /**
     * 下个地点字段
     */
    private String nextLocation = "nextLocation";

    /**
     * 地球经度
     */
    private String nextJdwgs84 = "nextJdwgs84";

    /**
     * 地球维度
     */
    private String nextWdwgs84 = "nextWdwgs84";

    /**
     * 逗留时间字段
     */
    private String stayTime = "stayTime";

    public StayAction(Integer threshold, String objectId, String place, String time, Class<IN> targetClass) {
        this.threshold = threshold;
        this.objectId = objectId;
        this.place = place;
        this.time = time;
        this.targetClass = targetClass;
    }

    public StayAction(Integer threshold, String objectId, String place, String time, Class<IN> targetClass, String nextPlace, String stayTime) {
        this.threshold = threshold;
        this.objectId = objectId;
        this.place = place;
        this.time = time;
        this.targetClass = targetClass;
        this.nextPlace = nextPlace;
        this.stayTime = stayTime;
    }

    public StayAction(Integer threshold, String objectId, String place, String location, String time, Class<IN> targetClass) {
        this.threshold = threshold;
        this.objectId = objectId;
        this.place = place;
        this.location = location;
        this.time = time;
        this.targetClass = targetClass;
    }

    public StayAction(Integer threshold, String objectId, String place, String location,
                      String jdwgs84, String wdwgs84,
                      String time, Class<IN> targetClass) {
        this.threshold = threshold;
        this.objectId = objectId;
        this.place = place;
        this.jdwgs84 = jdwgs84;
        this.wdwgs84 = wdwgs84;
        this.location = location;
        this.time = time;
        this.targetClass = targetClass;
    }

    @Override
    public JavaRDD<IN> doAction(SparkSession spark, JavaRDD<IN> in) {
        Dataset<Row> inData = spark.createDataFrame(in, targetClass);
        // 过滤同一地点重复数据，排除和上个地点，下个地点都相同的数据
        // 例如AAAABBBC，得到结果AABBC
        WindowSpec windowSpec = Window.partitionBy(objectId).orderBy(time);
        inData = inData.withColumn("repeat",
                col(place).equalTo(lag(col(place), 1).over(windowSpec))
                        .and(col(place).equalTo(lead(col(place), 1).over(windowSpec))));
        inData = inData.filter((FilterFunction<Row>) r->r.getAs("repeat") ==null || !(Boolean) r.getAs("repeat"));
        // 计算在相邻位置之间的时间间隔
        Dataset<Row> dfWithTimeDifference = inData
                .withColumn(stayTime, lead(col(time), 1).over(windowSpec).minus(col(time)))
                .withColumn(nextPlace, lead(col(place), 1).over(windowSpec))
                .withColumn(nextLocation, lead(col(location), 1).over(windowSpec))
                .withColumn(nextJdwgs84, lead(col(jdwgs84), 1).over(windowSpec))
                .withColumn(nextWdwgs84, lead(col(wdwgs84), 1).over(windowSpec))
                .withColumn("placeKey", concat(col(place), lead(col(place), 1).over(windowSpec)));
        // 计算百分位数
        WindowSpec windowSpecAgg = Window.partitionBy("placeKey");
        Dataset<Row> dfAggregated = dfWithTimeDifference
                .withColumn("quantile_stay_time", when(col(place).equalTo(col(nextPlace)), lit(0.0)).otherwise(
                        expr("percentile("+stayTime+", " + percentile + ")").over(windowSpecAgg)))
                // 输出一个percentitle为0.5（中位数）的值作为判断依据
                .withColumn("middleStayTime", when(col(place).equalTo(col(nextPlace)), lit(0.0)).otherwise(
                        expr("percentile("+stayTime+", 0.5)").over(windowSpecAgg)));
        // 计算阀值
        Dataset<Row> dfWithThreshold = dfAggregated.withColumn("threshold", col("quantile_stay_time").plus(threshold));
        // 过滤超时
        Dataset<Row> outData = dfWithThreshold
                .filter((FilterFunction<Row>)r->r.getAs(stayTime)!=null)
                // 在同一地点逗留的阀值不加中位数
                .filter((FilterFunction<Row>) r -> (Long) r.getAs(stayTime) >
                        (Objects.equals(r.getAs(place), r.getAs(nextPlace)) ? threshold :
                                (Double) r.getAs("threshold")));
        // 转换结果
        JavaRDD<IN> javaRDD = outData.as(Encoders.bean(targetClass)).toJavaRDD();
        return javaRDD;
    }
}
