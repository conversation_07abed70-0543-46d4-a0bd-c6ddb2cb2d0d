package com.trs.spark.action.impl.from;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.common.utils.StringUtils;
import com.trs.spark.action.AbstractNoInputFromAction;
import com.trs.spark.datasource.BaseSource;
import com.trs.spark.datasource.condition.SearchParams;
import com.trs.spark.function.Function;
import com.trs.spark.util.SourceUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * 使用baseSource的方式读取数据的行为
 *
 * <AUTHOR>
 */
public class BaseSourceReadAction<R extends Serializable> extends AbstractNoInputFromAction<JavaRDD<R>> {

    private String table;
    private String where;

    private String select;

    private String sql;

    private Class<R> clazz;

    private Function<Row, R> function;

    private Boolean useAsToBean;

    private BaseSource source;

    private SourceDriver sourceDriver;

    @Getter
    @Setter
    private Function<Dataset<Row>, Dataset<Row>> customDatasetRow;

    public BaseSourceReadAction addCustomDatasetRow(Function<Dataset<Row>, Dataset<Row>> customDatasetRow) {
        this.customDatasetRow = customDatasetRow;
        return this;
    }

    public BaseSourceReadAction(String url, String table, String user, String password, Function<Row, R> function) {
        this(url, table, user, password, null, null, function);
    }

    public BaseSourceReadAction(String url, String table, String user, String password, String where, Function<Row, R> function) {
        this(url, table, user, password, where, null, function);
    }

    public BaseSourceReadAction(String url, String table, String user, String password, String where, String select, Function<Row, R> function) {
        this.table = table;
        this.where = where;
        this.select = select;
        this.function = function;
        this.useAsToBean = false;
        sourceDriver = SourceDriverUtils.createSourceDriver(url, user, password).get();
        source = baseSource(sourceDriver);
    }

    public BaseSourceReadAction(String url, String table, String user, String password, String where, String select, String sql, Function<Row, R> function) {
        this.table = table;
        this.where = where;
        this.select = select;
        this.sql = sql;
        this.function = function;
        this.useAsToBean = false;
        sourceDriver = SourceDriverUtils.createSourceDriver(url, user, password).get();
        source = baseSource(sourceDriver);
    }

    public BaseSourceReadAction(String url, String table, String user, String password, Class<R> clazz) {
        this(url, table, user, password, null, null, clazz);
    }

    public BaseSourceReadAction(String url, String table, String user, String password, String where, Class<R> clazz) {
        this(url, table, user, password, where, null, clazz);
    }

    public BaseSourceReadAction(String url, String table, String user, String password, String where, String select, Class<R> clazz) {
        this.table = table;
        this.select = select;
        this.where = where;
        this.clazz = clazz;
        this.useAsToBean = true;
        sourceDriver = SourceDriverUtils.createSourceDriver(url, user, password).get();
        source = baseSource(sourceDriver);
    }

    public BaseSourceReadAction(String table, String where, String select, String sql, Class<R> clazz, BaseSource source, SourceDriver sourceDriver) {
        this.table = table;
        this.where = where;
        this.select = select;
        this.sql = sql;
        this.clazz = clazz;
        this.source = source;
        this.sourceDriver = sourceDriver;
        this.useAsToBean = true;
    }

    public BaseSourceReadAction(String table, String where, String select, String sql, Function<Row, R> function, BaseSource source, SourceDriver sourceDriver) {
        this.table = table;
        this.where = where;
        this.select = select;
        this.sql = sql;
        this.function = function;
        this.source = source;
        this.sourceDriver = sourceDriver;
        this.useAsToBean = false;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaRDD<R> doAction(SparkSession spark) {
        Dataset<Row> ds = getDataSet(spark);
        if (getCustomDatasetRow() != null) {
            return toJavaRdd(spark, getCustomDatasetRow().apply(ds));
        }
        return toJavaRdd(spark, ds);
    }

    public Dataset<Row> getDataSet(SparkSession spark) {
        SearchParams searchParams = searchParams();
        Dataset<Row> ds = source.loadDataFromSource(spark, sourceDriver, table, searchParams);
        return ds;
    }

    public Dataset<Row> getDataSet(SparkSession spark, SearchParams searchParams) {
        Dataset<Row> ds = source.loadDataFromSource(spark, sourceDriver, table, searchParams);
        return ds;
    }

    public JavaRDD<R> toJavaRdd(SparkSession spark, Dataset<Row> ds) {
        if (Boolean.TRUE.equals(useAsToBean)) {
            if (StringUtils.isNotEmpty(searchParams().getSelectFields())) {
                // 在过滤了字段的情况下 通过ds.as(Encoders.bean(clazz)).javaRDD()会异常
                return ds.javaRDD().map(row -> JSONObject.parseObject(row.json(), clazz));
            } else {
                try {
                    return ds.as(Encoders.bean(clazz)).javaRDD();
                } catch (Exception e) {
                    spark.sparkContext().logError(() -> "转换成javaRdd失败(将尝试更低效的方式转换成javaRdd)", e);
                    return ds.javaRDD().map(row -> JSONObject.parseObject(row.json(), clazz));
                }
            }
        }
        return ds.javaRDD().map(r -> function.apply(r));
    }

    /**
     * baseSource的实现
     *
     * @return
     */
    public BaseSource baseSource(SourceDriver sourceDriver) {
        return SourceUtils.makeSource(sourceDriver);
    }

    public SearchParams searchParams() {
        SearchParams searchParams = new SearchParams();
        searchParams.setWhere(where);
        searchParams.setSelectFields(select);
        searchParams.setSql(sql);
        return searchParams;
    }

    public String getTable() {
        return table;
    }
}
