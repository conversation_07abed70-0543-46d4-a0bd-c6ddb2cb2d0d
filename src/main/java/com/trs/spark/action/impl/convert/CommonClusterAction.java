package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.BiFunction;
import com.trs.spark.util.StreamUtils;
import lombok.val;
import org.apache.spark.HashPartitioner;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.graphx.Graph;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.storage.StorageLevel;
import scala.Option;
import scala.Tuple2;
import scala.reflect.ClassTag;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 通用的基于连通图的聚类分析
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/5/4 10:44
 * @since 1.0
 */
public class CommonClusterAction<IN extends Serializable>
        extends BaseConvertAction<JavaRDD<IN>, JavaPairRDD<IN, ArrayList<IN>>> {

    private BiFunction<IN, IN, Boolean> biFunction;

    /**
     * 簇中最少数量
     */
    private Long minPts;

    public CommonClusterAction(Long minPts, BiFunction<IN, IN, Boolean> biFunction) {
        this.minPts = Optional.ofNullable(minPts)
                .filter(r -> r > 0)
                .orElse(1L);
        this.biFunction = biFunction;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param data  输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<IN, ArrayList<IN>> doAction(SparkSession spark, JavaRDD<IN> data) {
        JavaPairRDD<Long, IN> rdd = data.zipWithUniqueId()
                .mapPartitionsToPair(items -> StreamUtils
                        .literators2Stream(items)
                        .map(item -> new Tuple2<>(item._2, item._1))
                        .iterator()
                );
        JavaPairRDD<Long, IN> rddTuple2 = rdd.partitioner().isPresent() ? rdd.cache() : rdd
                .partitionBy(new HashPartitioner(rdd.getNumPartitions()))
                .cache();
        val broadcast = spark.sparkContext().broadcast(rddTuple2.collect(), ClassTag.apply(List.class));
        val keywordsBlock = rddTuple2.mapPartitionsToPair(items -> StreamUtils
                        .literators2Stream(items)
                        .map(item -> {
                            val guid = item._1;
                            val keywords = item._2;
                            val sim_list = broadcast.value()
                                    .stream()
                                    .filter(obj -> {
                                        if (obj._1.equals(guid)) {
                                            return true;
                                        }
                                        return biFunction.apply(obj._2, keywords);
                                    }).map(i -> i._1)
                                    .collect(Collectors.toList());
                            return new Tuple2<>(guid, sim_list);
                        }).iterator())
                .flatMapToPair(obj -> obj._2
                        .stream()
                        .map(simId -> new Tuple2<Object, Object>(obj._1, simId))
                        .iterator()
                ).cache();
        val graph = Graph.fromEdgeTuples(
                keywordsBlock.rdd(),
                1,
                Option.empty(),
                StorageLevel.MEMORY_ONLY(),
                StorageLevel.MEMORY_ONLY(),
                ClassTag.Any()
        );
        val graphOps = Graph.graphToGraphOps(graph, ClassTag.Any(), ClassTag.Any());
        val graphCluster = graphOps.connectedComponents()
                .vertices()
                .toJavaRDD()
                .mapPartitionsToPair(items -> StreamUtils.literators2Stream(items)
                        .map(item -> new Tuple2<>((Long) item._1, (Long) item._2)).iterator());
        val rddClusterContent = rddTuple2.join(graphCluster, rddTuple2.partitioner().get())
                .mapPartitionsToPair(items -> StreamUtils.literators2Stream(items)
                        .map(item -> new Tuple2<>(item._2._2, item._2._1)).iterator())
                .groupByKey()
                .mapPartitionsToPair(items -> StreamUtils.literators2Stream(items)
                        .map(item -> new Tuple2<>(
                                item._1,
                                new ArrayList<>(StreamUtils.literators2Stream(item._2.iterator()).collect(Collectors.toList())))
                        ).iterator())
                .filter(i -> i._2.size() >= minPts);
        // 销毁缓存的数据
        keywordsBlock.unpersist();
        rddTuple2.unpersist();
        return rddTuple2.join(rddClusterContent, rddTuple2.partitioner().get())
                .mapPartitionsToPair(items -> StreamUtils.literators2Stream(items)
                        .map(item -> item._2)
                        .iterator());
    }
}
