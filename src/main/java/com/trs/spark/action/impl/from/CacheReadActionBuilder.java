package com.trs.spark.action.impl.from;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.CacheActionSourceFactory;
import com.trs.spark.cache.CacheAction;
import com.trs.spark.cache.CacheCoder;
import com.trs.spark.cache.coder.JsonArrayCoder;
import com.trs.spark.configuration.Configurations;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import static com.trs.spark.constant.CacheConstant.*;

/**
 * <AUTHOR>
 */
public class CacheReadActionBuilder<R extends Serializable> implements Serializable {

    /**
     * 从数据源加载数据的action
     */
    private BaseSourceReadAction<R> readAction;

    /**
     * 缓存操作的action
     */
    private CacheAction cacheAction;

    /**
     * 缓存数据的格式编解码器
     */
    private CacheCoder cacheCoder;

    /**
     * 从缓存加载数据时，字段类型映射
     */
    private Map<String, String> fileTypeMapping;

    /**
     * 公因条件
     */
    private String commonWhereCondition;

    /**
     * 缓存数据源设置
     * @param cacheAction
     * @return
     */
    public CacheReadActionBuilder<R> cacheAction(CacheAction cacheAction) {
        this.cacheAction = cacheAction;
        return this;
    }

    /**
     * 缓存格式的编码器 解码器设置
     * @param cacheCoder
     * @return
     */
    public CacheReadActionBuilder<R> cacheCoder(CacheCoder cacheCoder) {
        this.cacheCoder = cacheCoder;
        return this;
    }

    /**
     * 公因条件设置
     * @param commonCondition
     * @return
     */
    public CacheReadActionBuilder<R> commonCondition(String commonCondition) {
        this.commonWhereCondition = commonCondition;
        return this;
    }

    /**
     * 字段类型设置
     * 有时候缓存后再读取出来 数据回变得更宽泛 比如缓存前 int类型 缓存后就变成了bigint类型
     * 添加映射关系可以吧 bigint调整为int类型
     * @param fileName
     * @param dataType
     * @return
     */
    public CacheReadActionBuilder<R> addFieldType(String fileName, String dataType) {
         if (fileTypeMapping == null) {
             fileTypeMapping = new HashMap<>(0);
         }
         fileTypeMapping.put(fileName, dataType);
         return this;
    }

    public CacheReadAction<R> build() {
        if (null == cacheAction) {
            SourceDriver sourceDriver = SourceDriverUtils.createSourceDriver(
                    Configurations.getProperty(CACHE_URL).get(),
                    Configurations.getProperty(CACHE_USERNAME).orElse(""),
                    Configurations.getProperty(CACHE_PASSWORD).orElse("")
            ).get();
            cacheAction = CacheActionSourceFactory.actionFromDriver(sourceDriver);
        }
        if (null == cacheCoder) {
            cacheCoder = new JsonArrayCoder();
        }
        CacheReadAction<R> rCacheReadAction = new CacheReadAction<>(readAction, cacheAction, cacheCoder, fileTypeMapping, commonWhereCondition);
        return rCacheReadAction;
    }

    public static <T extends Serializable> CacheReadActionBuilder newBuilder(BaseSourceReadAction<T> readAction) {
        return newBuilder(readAction, null);
    }

    public static <T extends Serializable> CacheReadActionBuilder newBuilder(BaseSourceReadAction<T> readAction, String commonWhereCondition) {
        CacheReadActionBuilder<T> builder = new CacheReadActionBuilder();
        builder.readAction = readAction;
        builder.commonWhereCondition = commonWhereCondition;
        return builder;
    }
}
