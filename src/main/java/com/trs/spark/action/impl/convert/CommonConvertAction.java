package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.Function;
import lombok.Getter;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 通用转换器
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 14:27
 * @since 1.0
 */
public class CommonConvertAction<IN extends Serializable, OUT extends Serializable>
        extends BaseConvertAction<IN, OUT> {

    @Getter
    private Function<IN, OUT> function;

    public CommonConvertAction(Function<IN, OUT> function) {
        this.function = function;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param in    输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public OUT doAction(SparkSession spark, IN in) {
        return getFunction().apply(in);
    }
}
