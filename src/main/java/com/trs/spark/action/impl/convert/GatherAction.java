package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.Function;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 聚合算子
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/3/22 14:53
 * @version 1.0
 * @since 1.0
 */
public class GatherAction<IN extends Serializable> extends BaseConvertAction<JavaRDD<IN>, JavaPairRDD<String, ArrayList<IN>>> {

    private Integer threshold;

    private Function<IN, ArrayList<String>> makeStandardKey;

    private Function<ArrayList<IN>, ArrayList<IN>> distinctData;


    public GatherAction(Integer threshold, Function<IN, ArrayList<String>> makeStandardKey) {
        this(threshold, makeStandardKey, (ArrayList<IN> list) -> {
            ArrayList<IN> result = new ArrayList<>(list.size());
            for (IN in : list) {
                if (!result.contains(in)) {
                    result.add(in);
                }
            }
            return result;
        });
    }

    public GatherAction(Integer threshold, Function<IN, ArrayList<String>> makeStandardKey, Function<ArrayList<IN>, ArrayList<IN>> distinctData) {
        this.threshold = threshold;
        this.makeStandardKey = makeStandardKey;
        this.distinctData = distinctData;
    }

    @Override
    public JavaPairRDD<String, ArrayList<IN>> doAction(SparkSession spark, JavaRDD<IN> in) {
        //根据intervalTime时间，来生成statisticTime
        //并且根据生成的statisticTime队列生成新的RDD[GatherEntity]
        JavaPairRDD<String, IN> gPairRDD = in.flatMapToPair(item -> {
            List<Tuple2<String, IN>> data = new ArrayList<>();
            makeStandardKey.apply(item).forEach(key -> data.add(new Tuple2<>(key, item)));
            return data.iterator();
        });
        //进行groupBy操作，并保证同一个key里不会出现重复的objectId
        JavaPairRDD<String, ArrayList<IN>> gatherByKey = gPairRDD.aggregateByKey(
                (new ArrayList<>()),
                (v1, v2) -> {
                    v1.add(v2);
                    return distinctData.apply(v1);
                },
                (pv1, pv2) -> {
                    pv1.addAll(pv2);
                    return distinctData.apply(pv1);
                });
        //排除掉同一时间同一地点的重复数据，重复数据规则为objectId为一样的
        return gatherByKey.mapPartitionsToPair(items -> {
            List<Tuple2<String, ArrayList<IN>>> data = new ArrayList<>();
            while (items.hasNext()) {
                Tuple2<String, ArrayList<IN>> next = items.next();
                data.add(new Tuple2<>(next._1, distinctData.apply(next._2)));
            }
            return data.iterator();
        }).filter(a -> a._2.size() > threshold);
    }

}
