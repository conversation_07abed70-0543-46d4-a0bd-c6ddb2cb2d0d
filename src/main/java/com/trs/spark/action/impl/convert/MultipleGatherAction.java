package com.trs.spark.action.impl.convert;

import com.trs.common.utils.TimeUtils;
import com.trs.spark.action.BaseConvertAction;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.api.java.UDF2;
import org.apache.spark.sql.api.java.UDF3;
import org.apache.spark.sql.expressions.UserDefinedFunction;
import org.apache.spark.sql.types.DataTypes;

import java.io.Serializable;

import static org.apache.spark.sql.functions.*;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR>
 * @date 2023/6/15
 * 多感知源聚合算子，使用DataSet实现
 */
public class MultipleGatherAction<IN extends Serializable> extends BaseConvertAction<JavaRDD<IN>, JavaRDD<IN>> {

    /**
     * 1纬度距离
     */
    public static double baseDistance = 111111.0;

    /**
     * 阀值
     */
    private Integer threshold;

    /**
     * 距离阀值
     */
    private Integer distanceThreshold;

    /**
     * 时间间隔阀值
     */
    private Integer intervalTime;

    /**
     * 特征值字段
     */
    private String objectId;

    /**
     * 地球经度字段
     */
    private String jdwgs84;

    /**
     * 地球维度字段
     */
    private String wdwgs84;

    /**
     * 活动时间字段
     */
    private String time;

    /**
     * 开始时间字段
     */
    private String startTime;

    /**
     * 目标类型
     */
    private Class targetClass;

    private String key = "key";

    private String statisticTime = "statisticTime";

    private String newWdwgs84  = "newWdwgs84";

    private String newJdwgs84  = "newJdwgs84";

    private String newGeohash = "newGeohash";

    public MultipleGatherAction threshold(Integer threshold) {
        this.threshold = threshold;
        return this;
    }

    public MultipleGatherAction distanceThreshold(Integer distanceThreshold) {
        this.distanceThreshold = distanceThreshold;
        return this;
    }

    public MultipleGatherAction intervalTime(Integer intervalTime) {
        this.intervalTime = intervalTime;
        return this;
    }

    public MultipleGatherAction objectId(String objectId) {
        this.objectId = objectId;
        return this;
    }

    public MultipleGatherAction jdwgs84(String jdwgs84) {
        this.jdwgs84 = jdwgs84;
        return this;
    }

    public MultipleGatherAction wdwgs84(String wdwgs84) {
        this.wdwgs84 = wdwgs84;
        return this;
    }

    public MultipleGatherAction time(String time) {
        this.time = time;
        return this;
    }

    public MultipleGatherAction startTime(String startTime) {
        this.startTime = startTime;
        return this;
    }

    public MultipleGatherAction targetClass(Class targetClass) {
        this.targetClass = targetClass;
        return this;
    }

    public MultipleGatherAction(Integer threshold, Integer intervalTime, String objectId, String startTime, String time, Class targetClass) {
        this.threshold = threshold;
        this.intervalTime = intervalTime;
        this.objectId = objectId;
        this.startTime = startTime;
        this.time = time;
        this.targetClass = targetClass;
    }

    public MultipleGatherAction() {
    }

    @Override
    public JavaRDD<IN> doAction(SparkSession spark, JavaRDD<IN> in) {
        Dataset<Row> inData = spark.createDataFrame(in, targetClass);
        // 构建分组key：转换后的时间+地点
        // 转换后的时间 = 开始时间 + 时间间隔 * ((当前时间-开始时间) mod 时间间隔)
        Dataset<Row> data = inData
                .withColumn(statisticTime, date_format(
                        col(startTime).plus(
                                expr("interval " + intervalTime + " minutes").multiply(
                                        floor(unix_timestamp(col(time))
                                                .minus(unix_timestamp(col(startTime)))
                                                .divide(intervalTime * 60)))),
                        TimeUtils.YYYYMMDD_HHMMSS2));
        // 注册自定义函数，计算新的经纬度
        spark.udf().register("newWdwgs84", (UDF2<Double, Integer, Double>) MultipleGatherAction::newWdwgs84, DataTypes.DoubleType);
        spark.udf().register("newJdwgs84", (UDF3<Double, Double, Integer, Double>) MultipleGatherAction::newjdwgs84, DataTypes.DoubleType);
        UserDefinedFunction newWdwgs84Function = udf((Double wdwgs84) ->
                newWdwgs84(wdwgs84, distanceThreshold), DataTypes.DoubleType);
        UserDefinedFunction newJdwgs84Function = udf((Double jdwgs84, Double wdwgs8) ->
                newjdwgs84(jdwgs84, wdwgs8, distanceThreshold), DataTypes.DoubleType);
        data = data
                .withColumn(newWdwgs84, newWdwgs84Function.apply(col(wdwgs84)))
                .withColumn(newJdwgs84, newJdwgs84Function.apply(col(jdwgs84), col(newWdwgs84)))
                .withColumn(newGeohash,  concat(col(newWdwgs84), lit("-"), col(newJdwgs84)))
                .withColumn(key, concat(col(statisticTime), lit("-"), col(newGeohash)));
        // 分组，过滤每组大于阀值的数据，最后只保留key
        String list = "list";
        Dataset<Row> keyData = data.groupBy(key)
                .agg(collect_set(struct(objectId)).alias(list))
                .filter(size(col(list)).geq(threshold))
                .drop(list);
        // 根据key过滤数据
        data = keyData.join(data, key);
        // 每个聚集中每个人只保留一条记录
        Dataset<Row> timeData = data.groupBy(key, objectId)
                .agg(first("recordid").alias("recordid1"))
                .withColumnRenamed(key, "key1")
                .withColumnRenamed(objectId, "objectId1");
        data = timeData.join(data, col("key1").equalTo(col(key))
                        .and(col("recordid1").equalTo(col("recordid")))
                        .and(col("objectId1").equalTo(col(objectId))),
                        "inner");
        // 转换结果
        JavaRDD<IN> javaRDD = data.as(Encoders.bean(targetClass)).toJavaRDD();
        return javaRDD;
    }

    private static double newWdwgs84(Double wdwgs84, Integer distanceThreshold) {
        //纬度转化为距离，整除阀值，乘以阀值，转化为纬度
        return new Double(wdwgs84 * baseDistance / distanceThreshold).intValue() * distanceThreshold / baseDistance;
    }

    private static double newjdwgs84(Double jdwgs84, Double wdwgs84, Integer distanceThreshold) {
        // 经度的距离跟纬度的距离有关联
        double radians = Math.toRadians(wdwgs84);
        double metersPerDegree = baseDistance * Math.cos(radians);
        return new Double(jdwgs84 * metersPerDegree / distanceThreshold).intValue() * distanceThreshold / metersPerDegree;
    }
}
