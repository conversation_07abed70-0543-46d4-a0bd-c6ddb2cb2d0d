package com.trs.spark.action.impl.peek;

import com.trs.common.datasource.SourceDriver;
import com.trs.common.datasource.SourceDriverUtils;
import com.trs.spark.action.BasePeekAction;
import com.trs.spark.datasource.BaseSource;
import com.trs.spark.function.CheckedFunction;
import com.trs.spark.util.SourceUtils;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * <AUTHOR>
 */
public class BaseSourceWriteAction<IN extends Serializable, Save extends Serializable> extends BasePeekAction<JavaRDD<IN>> {

    private CheckedFunction<IN, Save> convertor;

    private SourceDriver sourceDriver;

    private String tableName;

    private SaveMode saveMode;

    private Class<Save> targetClass;

    protected BaseSource source;

    public BaseSourceWriteAction(String url,
                                 String userName,
                                 String password,
                                 String tableName,
                                 SaveMode saveMode,
                                 CheckedFunction<IN, Save> convertor,
                                 Class<Save> targetClass) {
        this(
                SourceDriverUtils.createSourceDriver(checkNotNull(url), checkNotNull(userName), checkNotNull(password))
                        .getOrNPException("can not get driver as " + url),
                tableName,
                saveMode,
                convertor,
                targetClass
        );
    }

    public BaseSourceWriteAction(
            SourceDriver sourceDriver,
            String tableName,
            SaveMode saveMode,
            CheckedFunction<IN, Save> convertor,
            Class<Save> targetClass
    ) {
        this.sourceDriver = sourceDriver;
        this.tableName = tableName;
        this.saveMode = saveMode;
        this.convertor = convertor;
        this.targetClass = targetClass;
        this.source = buildSource(sourceDriver);
    }


    /**
     * 进行peek操作<BR>
     *
     * @param spark SparkSession
     * @param rdd   输入数据
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/21 14:33
     */
    @Override
    public void doPeek(SparkSession spark, JavaRDD<IN> rdd) {
        source.writeDataToSource(spark, sourceDriver, tableName, rdd, saveMode, convertor, targetClass);
    }

    public BaseSource buildSource(SourceDriver sourceDriver){
        return SourceUtils.makeSource(sourceDriver);
    }
}
