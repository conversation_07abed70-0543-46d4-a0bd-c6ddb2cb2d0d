package com.trs.spark.action.impl.convert;

import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.function.Function;
import com.trs.spark.util.StreamUtils;
import lombok.Getter;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 转换
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2023/3/24 16:01
 * @version 1.0
 * @since 1.0
 */
public class JavaRddToJavaPairRddConvertAction<Key extends Serializable, IN extends Serializable, OUT extends Serializable>
        extends BaseConvertAction<JavaRDD<IN>, JavaPairRDD<Key, OUT>> {

    @Getter
    private Function<IN, Key> keyFunction;

    @Getter
    private Function<IN, OUT> outFunction;

    public JavaRddToJavaPairRddConvertAction(Function<IN, Key> keyFunction, Function<IN, OUT> outFunction) {
        this.keyFunction = keyFunction;
        this.outFunction = outFunction;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param data  输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<Key, OUT> doAction(SparkSession spark, JavaRDD<IN> data) {
        return data.mapPartitionsToPair(
                items -> StreamUtils.literators2Stream(items)
                        .map(item -> new Tuple2<>(
                                getKeyFunction().apply(item),
                                getOutFunction().apply(item))
                        ).iterator()
        );
    }
}
