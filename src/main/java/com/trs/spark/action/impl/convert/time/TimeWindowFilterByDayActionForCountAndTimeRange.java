package com.trs.spark.action.impl.convert.time;

import com.trs.common.utils.TimeUtils;
import com.trs.spark.action.BaseTimeWindowFilterByAnyActionForCountAndTimeRange;
import com.trs.spark.dto.TimeWindowCountDTO;
import com.trs.spark.function.Function;
import com.trs.spark.vo.CountAndTimeRangeVO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/5/15 09:19
 * @since 1.0
 */
public class TimeWindowFilterByDayActionForCountAndTimeRange<IN extends Serializable> extends BaseTimeWindowFilterByAnyActionForCountAndTimeRange<IN> {

    public TimeWindowFilterByDayActionForCountAndTimeRange(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan) {
        super(makeKey, makeTime, threshold, timeSpan);
    }

    public TimeWindowFilterByDayActionForCountAndTimeRange(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan, Boolean distinct) {
        super(makeKey, makeTime, threshold, timeSpan, distinct);
    }

    /**
     * 获取对应时间跨度内最大长度<BR>
     *
     * @param list     相关数据列表
     * @param timeSpan 时间跨度
     * @param distinct 是否排重
     * @return 获取对应时间跨度内最大长度
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/17 11:38
     */
    @Override
    protected CountAndTimeRangeVO<IN> maxCount(List<TimeWindowCountDTO<IN>> list, Integer timeSpan, Boolean distinct) {
        CountAndTimeRangeVO<IN> maxCount = new CountAndTimeRangeVO<>();
        if (list.isEmpty()) {
            return maxCount;
        } else if (list.size() == 1) {
            TimeWindowCountDTO<IN> data = list.get(0);
            return new CountAndTimeRangeVO<>(data.getData(), data.getTime());
        }
        for (int i = 0, len = list.size(); i < len; i++) {
            TimeWindowCountDTO<IN> start = list.get(i);
            List<TimeWindowCountDTO<IN>> tmp = list
                    .stream()
                    .filter(r -> Math.abs(start.getTime().getTime() - r.getTime().getTime()) <= timeSpan * 24 * 60 * 60 * 1000L)
                    .collect(Collectors.toList());
            if (tmp.isEmpty()) {
                continue;
            }
            CountAndTimeRangeVO<IN> count = new CountAndTimeRangeVO<>(
                    start.getData(),
                    (long) tmp.size(),
                    tmp.get(0).getTime(),
                    tmp.get(tmp.size() - 1).getTime()
            );
            if (distinct) {
                count.setCount(tmp.stream().map(r -> TimeUtils.dateToString(r.getTime(), TimeUtils.YYYYMMDD)).distinct().count());
            }
            if (count.getCount() > maxCount.getCount()) {
                maxCount = count;
            }
        }
        return maxCount;
    }
}
