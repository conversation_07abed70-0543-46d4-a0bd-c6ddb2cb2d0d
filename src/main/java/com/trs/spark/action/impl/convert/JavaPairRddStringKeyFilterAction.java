package com.trs.spark.action.impl.convert;

import com.trs.spark.function.Predicate;
import scala.Tuple2;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * javaPairRdd过滤
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 17:58
 * @version 1.0
 * @since 1.0
 */
public class JavaPairRddStringKeyFilterAction<IN extends Serializable>
        extends JavaPairRddFilterAction<String, IN> {

    public JavaPairRddStringKeyFilterAction(Predicate<Tuple2<String, IN>> predicate) {
        super(predicate);
    }

}
