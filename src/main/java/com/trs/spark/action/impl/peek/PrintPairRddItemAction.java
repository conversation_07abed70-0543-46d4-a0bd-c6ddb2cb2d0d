package com.trs.spark.action.impl.peek;

import com.trs.spark.function.Consumer;
import com.trs.spark.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.JavaPairRDD;
import scala.Tuple2;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * PrintRddAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:34
 * @since 1.0
 */
@Slf4j
public class PrintPairRddItemAction<Key extends Serializable, IN extends Serializable>
        extends BasePrintAction<JavaPairRDD<Key, IN>> {

    public PrintPairRddItemAction() {
        this(
                info -> "this pairRddItem = " + info,
                log::info,
                true
        );
    }

    public PrintPairRddItemAction(boolean openPrint) {
        this(
                info -> "this pairRddItem = " + info,
                log::info,
                openPrint
        );
    }

    public PrintPairRddItemAction(Function<Tuple2<Key, IN>, String> makeOutInfo) {
        this(
                makeOutInfo,
                log::info,
                true
        );
    }


    public PrintPairRddItemAction(Function<Tuple2<Key, IN>, String> makeOutInfo, boolean openPrint) {
        this(
                makeOutInfo,
                log::info,
                openPrint
        );
    }

    public PrintPairRddItemAction(Consumer<String> printInfo) {
        this(
                info -> "this pairRddItem = " + info,
                printInfo,
                true
        );
    }

    public PrintPairRddItemAction(Consumer<String> printInfo, boolean openPrint) {
        this(
                info -> "this pairRddItem = " + info,
                printInfo,
                openPrint
        );
    }

    public PrintPairRddItemAction(Function<Tuple2<Key, IN>, String> makeOutInfo, Consumer<String> printInfo) {
        this(makeOutInfo, printInfo, true);
    }

    public PrintPairRddItemAction(Function<Tuple2<Key, IN>, String> makeOutInfo, Consumer<String> printInfo, boolean openPrint) {
        super(
                data -> data.foreachPartition(items -> items.forEachRemaining(item -> printInfo
                        .accept(makeOutInfo.apply(item)))),
                openPrint
        );
    }
}
