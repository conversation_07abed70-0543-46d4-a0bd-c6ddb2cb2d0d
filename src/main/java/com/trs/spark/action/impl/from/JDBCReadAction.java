package com.trs.spark.action.impl.from;

import com.trs.common.utils.StringUtils;
import com.trs.spark.action.AbstractNoInputFromAction;
import com.trs.spark.function.Function;
import com.trs.spark.util.SourceUtils;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.*;

import java.io.Serializable;
import java.util.Properties;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 使用jdbc读取数据
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 13:01
 * @version 1.0
 * @since 1.0
 */
public class JDBCReadAction<R extends Serializable> extends AbstractNoInputFromAction<JavaRDD<R>> {

    private String url;
    private String table;
    private String user;
    private String password;
    private String where;

    private String select;


    private Class<R> clazz;

    private Function<Row, R> function;

    private Boolean useAsToBean;

    public JDBCReadAction(String url, String table, String user, String password, Function<Row, R> function) {
        this(url, table, user, password, null, null, function);
    }

    public JDBCReadAction(String url, String table, String user, String password, String where, Function<Row, R> function) {
        this(url, table, user, password, where, null, function);
    }

    public JDBCReadAction(String url, String table, String user, String password, String where, String select, Function<Row, R> function) {
        this.url = url;
        this.table = table;
        this.user = user;
        this.password = password;
        this.where = where;
        this.select = select;
        this.function = function;
        this.useAsToBean = false;
    }

    public JDBCReadAction(String url, String table, String user, String password, Class<R> clazz) {
        this(url, table, user, password, null, null, clazz);
    }

    public JDBCReadAction(String url, String table, String user, String password, String where, Class<R> clazz) {
        this(url, table, user, password, where, null, clazz);
    }

    public JDBCReadAction(String url, String table, String user, String password, String where, String select, Class<R> clazz) {
        this.url = url;
        this.table = table;
        this.user = user;
        this.password = password;
        this.select = select;
        this.where = where;
        this.clazz = clazz;
        this.useAsToBean = true;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaRDD<R> doAction(SparkSession spark) {
        Properties properties = new Properties();
        if (StringUtils.isNotEmpty(user)) {
            properties.put("user", user);
        }
        if (StringUtils.isNotEmpty(password)) {
            properties.put("password", password);
        }
        Dataset<Row> ds = spark
                .read()
                .jdbc(url, table, properties);
        Column[] columns = SourceUtils.makeColumns(select);
        if (columns != null && columns.length > 0) {
            ds = ds.select(columns);
        }
        if (StringUtils.isNotEmpty(where)) {
            ds = ds.where(where);
        }
        if (useAsToBean) {
            return ds.as(Encoders.bean(clazz)).javaRDD();
        }
        return ds.javaRDD().map(r -> function.apply(r));
    }
}
