package com.trs.spark.action.impl.convert;

import com.trs.spark.function.Predicate;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 11:58
 * @since 1.0
 */
public class JavaRddFilterAction<IN extends Serializable>
        extends JavaRddFilterAndConvertAction<IN, IN> {

    public JavaRddFilterAction(Predicate<IN> predicate) {
        super(predicate, item -> item);
    }
}
