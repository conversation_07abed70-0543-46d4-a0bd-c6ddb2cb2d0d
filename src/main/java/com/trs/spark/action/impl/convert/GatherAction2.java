package com.trs.spark.action.impl.convert;

import com.trs.common.utils.TimeUtils;
import com.trs.spark.action.BaseConvertAction;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

import static org.apache.spark.sql.functions.*;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR>
 * @date 2023/6/15
 * 新聚合算子，使用DataSet实现
 */
public class GatherAction2<IN extends Serializable> extends BaseConvertAction<JavaRDD<IN>, JavaRDD<IN>> {

    /**
     * 阀值
     */
    private Integer threshold;

    /**
     * 时间间隔阀值
     */
    private Integer intervalTime;

    /**
     * 特征值字段
     */
    private String objectId;

    /**
     * 活动地点字段
     */
    private String place;

    /**
     * 活动时间字段
     */
    private String time;

    /**
     * 开始时间字段
     */
    private String startTime;

    /**
     * 目标类型
     */
    private Class targetClass;

    private String key = "key";

    private String statisticTime = "statisticTime";

    public GatherAction2(Integer threshold, Integer intervalTime, String objectId, String place, String startTime, String time, Class targetClass) {
        this.threshold = threshold;
        this.intervalTime = intervalTime;
        this.objectId = objectId;
        this.place = place;
        this.startTime = startTime;
        this.time = time;
        this.targetClass = targetClass;
    }

    @Override
    public JavaRDD<IN> doAction(SparkSession spark, JavaRDD<IN> in) {
        Dataset<Row> inData = spark.createDataFrame(in, targetClass);
        // 构建分组key：转换后的时间+地点
        // 转换后的时间 = 开始时间 + 时间间隔 * ((当前时间-开始时间) mod 时间间隔)
        Dataset<Row> data = inData
                .withColumn(statisticTime, date_format(
                        col(startTime).plus(
                                expr("interval " + intervalTime + " minutes").multiply(
                                        floor(
                                                //计算开始时间和活动时间的间隔时间
                                                unix_timestamp(col(time)).minus(unix_timestamp(col(startTime)))

                                                .divide(intervalTime * 60)
                                        )
                                )
                        ),
                        TimeUtils.YYYYMMDD_HHMMSS2))
                .withColumn(key, concat(col(statisticTime), lit("-"), col(place)));
        // 分组，过滤每组大于阀值的数据，最后只保留key
        String list = "list";
        Dataset<Row> keyData = data.groupBy(key)
                .agg(collect_set(struct(objectId)).alias(list))
                .filter(size(col(list)).geq(threshold))
                .drop(list);
        // 根据key过滤数据
        data = keyData.join(data, key);
        // 每个聚集中每个人只保留一条记录
        Dataset<Row> timeData = data.groupBy(key, objectId)
                .agg(first("recordid").alias("recordid1"))
                .withColumnRenamed(key, "key1")
                .withColumnRenamed(objectId, "objectId1");
        data = timeData.join(data, col("key1").equalTo(col(key))
                        .and(col("recordid1").equalTo(col("recordid")))
                        .and(col("objectId1").equalTo(col(objectId))),
                "inner");
        // 转换结果
        JavaRDD<IN> javaRDD = data.as(Encoders.bean(targetClass)).toJavaRDD();
        return javaRDD;
    }
}
