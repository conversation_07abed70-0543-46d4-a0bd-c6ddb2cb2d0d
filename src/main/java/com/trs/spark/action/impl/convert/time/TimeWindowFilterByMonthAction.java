package com.trs.spark.action.impl.convert.time;

import com.trs.common.utils.TimeUtils;
import com.trs.spark.action.BaseTimeWindowFilterByAnyActionForLong;
import com.trs.spark.function.Function;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/5/15 09:19
 * @since 1.0
 */
public class TimeWindowFilterByMonthAction<IN extends Serializable> extends BaseTimeWindowFilterByAnyActionForLong<IN> {

    public TimeWindowFilterByMonthAction(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan) {
        super(makeKey, makeTime, threshold, timeSpan);
    }

    public TimeWindowFilterByMonthAction(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan, Boolean distinct) {
        super(makeKey, makeTime, threshold, timeSpan, distinct);
    }

    /**
     * 获取对应时间跨度内最大长度<BR>
     *
     * @param date     时间列表
     * @param timeSpan 时间跨度
     * @param distinct 是否排重
     * @return 获取对应时间跨度内最大长度
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/17 11:38
     */
    @Override
    protected Long doMaxCount(List<Date> date, Integer timeSpan, Boolean distinct) {
        if (date.isEmpty()) {
            return 0L;
        } else if (date.size() == 1) {
            return 1L;
        }
        Long maxCount = 1L;
        for (int i = 0, len = date.size(); i < len; i++) {
            Date start = date.get(i);
            Stream<String> tmp = date.stream()
                    .filter(r -> Math.abs(start.getTime() - r.getTime()) <= timeSpan * 24 * 60 * 60 * 1000L)
                    .map(r -> TimeUtils.dateToString(r, TimeUtils.YYYYMMDD4));
            Long count = distinct ? tmp.distinct().count() : tmp.count();
            maxCount = Math.max(maxCount, count);
        }
        return maxCount;
    }
}
