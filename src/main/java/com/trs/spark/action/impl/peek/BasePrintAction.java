package com.trs.spark.action.impl.peek;

import com.trs.spark.action.BasePeekAction;
import com.trs.spark.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * BasePrintAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/4/11 15:34
 * @since 1.0
 */
@Slf4j
public abstract class BasePrintAction<IN extends Serializable> extends BasePeekAction<IN> {
    private boolean openPrint;
    private Consumer<IN> consumer;

    public BasePrintAction(Consumer<IN> consumer, boolean openPrint) {
        this.consumer = consumer;
        this.openPrint = openPrint;
    }

    /**
     * 进行peek操作<BR>
     *
     * @param spark SparkSession
     * @param data  输入数据
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/21 14:33
     */
    @Override
    public void doPeek(SparkSession spark, IN data) {
        if (openPrint) {
            consumer.accept(data);
        }
    }
}
