package com.trs.spark.action;

import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 基础的peek操作
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 12:48
 * @version 1.0
 * @since 1.0
 */
public abstract class BasePeekAction<IN extends Serializable> extends BaseAction<IN, IN> {
    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param in    输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public final IN doAction(SparkSession spark, IN in) {
        final IN tmp = in;
        doPeek(spark, tmp);
        return in;
    }

    /**
     * 进行peek操作<BR>
     *
     * @param spark SparkSession
     * @param data  输入数据
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/21 14:33
     */
    public abstract void doPeek(SparkSession spark, IN data);
}
