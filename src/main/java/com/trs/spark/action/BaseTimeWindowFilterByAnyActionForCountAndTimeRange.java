package com.trs.spark.action;

import com.trs.spark.function.Function;
import com.trs.spark.vo.CountAndTimeRangeVO;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseTimeWindowFilterByXActionForLong
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/5/30 19:06
 * @since 1.0
 */
public abstract class BaseTimeWindowFilterByAnyActionForCountAndTimeRange<IN extends Serializable> extends BaseTimeWindowFilterByAnyAction<IN, CountAndTimeRangeVO<IN>> {

    public BaseTimeWindowFilterByAnyActionForCountAndTimeRange(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan) {
        super(makeKey, makeTime, threshold, timeSpan);
    }

    public BaseTimeWindowFilterByAnyActionForCountAndTimeRange(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan, Boolean distinct) {
        super(makeKey, makeTime, threshold, timeSpan, distinct);
    }

    /**
     * 构建初始化的Out对象<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/30 19:00
     */
    @Override
    public CountAndTimeRangeVO<IN> makeOutInitEntity() {
        return new CountAndTimeRangeVO<>();
    }

    /**
     * 检测输出的Out对象是否命中<BR>
     *
     * @param vo        参数
     * @param threshold 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/30 19:02
     */
    @Override
    public Boolean checkOutIsHitThreshold(CountAndTimeRangeVO<IN> vo, Integer threshold) {
        if (vo == null
                || vo.getData() == null
                || vo.getCount() == 0L) {
            return false;
        }
        return vo.getCount() >= threshold.longValue();
    }
}
