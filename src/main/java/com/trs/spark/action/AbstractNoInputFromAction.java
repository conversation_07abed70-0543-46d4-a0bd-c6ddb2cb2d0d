package com.trs.spark.action;

import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 没有输入的From计算
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 13:00
 * @version 1.0
 * @since 1.0
 */
public abstract class AbstractNoInputFromAction<OUTPUT extends Serializable> extends BaseFromAction<Serializable, OUTPUT> {
    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param s     输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public final OUTPUT doAction(SparkSession spark, Serializable s) {
        return doAction(spark);
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    public abstract OUTPUT doAction(SparkSession spark);
}
