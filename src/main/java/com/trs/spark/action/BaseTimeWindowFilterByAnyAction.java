package com.trs.spark.action;

import com.trs.common.utils.StringUtils;
import com.trs.spark.dto.TimeWindowCountDTO;
import com.trs.spark.function.Function;
import com.trs.spark.util.StreamUtils;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.sql.SparkSession;
import scala.Tuple2;

import java.io.Serializable;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/5/15 09:19
 * @since 1.0
 */
public abstract class BaseTimeWindowFilterByAnyAction<IN extends Serializable, OUT extends Serializable> extends BaseConvertAction<JavaRDD<IN>, JavaPairRDD<String, OUT>> {

    /**
     * 获取数据对应的Key
     */
    private Function<IN, String> makeKey;

    /**
     * 获取数据对应的时间
     */
    private Function<IN, Date> makeTime;

    /**
     * 阈值
     */
    private Integer threshold;

    /**
     * 时间跨度
     */
    private Integer timeSpan;

    /**
     * 同一天是否排重
     */
    private Boolean distinct;

    public BaseTimeWindowFilterByAnyAction(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan) {
        this(makeKey, makeTime, threshold, timeSpan, true);
    }

    public BaseTimeWindowFilterByAnyAction(Function<IN, String> makeKey, Function<IN, Date> makeTime, Integer threshold, Integer timeSpan, Boolean distinct) {
        this.makeKey = makeKey;
        this.makeTime = makeTime;
        this.threshold = threshold;
        this.timeSpan = timeSpan;
        this.distinct = distinct;
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param data  输入数据
     * @return 运算结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public JavaPairRDD<String, OUT> doAction(SparkSession spark, JavaRDD<IN> data) {
        return data.mapPartitionsToPair(
                        items -> StreamUtils
                                .literators2Stream(items)
                                .map(r -> new Tuple2<>(
                                        makeKey.apply(r),
                                        new TimeWindowCountDTO<>(r, makeKey.apply(r), makeTime.apply(r))
                                )).filter(r -> StringUtils.isNotEmpty(r._1))
                                .iterator()
                ).groupByKey()
                .mapPartitionsToPair(items -> StreamUtils.literators2Stream(items)
                        .map(item -> {
                            List<TimeWindowCountDTO<IN>> list = StreamUtils.literators2Stream(item._2.iterator())
                                    .distinct()
                                    .sorted(Comparator.comparing(TimeWindowCountDTO::getTime))
                                    .collect(Collectors.toList());
                            OUT count = makeOutInitEntity();
                            if (list.size() >= threshold) {
                                count = maxCount(list, timeSpan, distinct);
                            }
                            return new Tuple2<>(item._1, count);
                        }).iterator()).filter(items -> checkOutIsHitThreshold(items._2, threshold));
    }

    /**
     * 构建初始化的Out对象<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/30 19:00
     */
    public abstract OUT makeOutInitEntity();

    /**
     * 检测输出的Out对象是否命中<BR>
     *
     * @param out       参数
     * @param threshold 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/30 19:02
     */
    public abstract Boolean checkOutIsHitThreshold(OUT out, Integer threshold);

    /**
     * 获取对应时间跨度内最大长度<BR>
     *
     * @param list     相关数据列表
     * @param timeSpan 时间跨度
     * @param distinct 是否排重
     * @return 获取对应时间跨度内最大长度
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/5/17 11:38
     */
    protected abstract OUT maxCount(List<TimeWindowCountDTO<IN>> list, Integer timeSpan, Boolean distinct);
}
