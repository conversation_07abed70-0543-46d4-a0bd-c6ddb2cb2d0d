package com.trs.spark.action;

import com.trs.spark.context.IActionContext;
import com.trs.spark.context.SimpleActionContext;
import org.apache.spark.sql.SparkSession;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * 基础Action
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 12:02
 * @version 1.0
 * @since 1.0
 */
public abstract class BaseAction<IN extends Serializable, OUT extends Serializable>
        implements IAction<IN, OUT> {

    private IActionContext<OUT> context;

    public BaseAction() {
        makeContext();
    }

    public void makeContext() {
        this.context = new SimpleActionContext<>();
    }

    /**
     * 进行相关运算<BR>
     *
     * @param spark SparkSession
     * @param in    输入数据
     * @return 运算情况
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:31
     */
    @Override
    public Boolean action(SparkSession spark, IN in) {
        getActionContext().setExecutedResult(doAction(spark, in));
        return true;
    }

    /**
     * 获取操作的执行结果<BR>
     *
     * @return 执行结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:32
     */
    @Override
    public OUT getExecutedResult() {
        return getActionContext().getExecutedResult();
    }

    /**
     * 获取当前上下文<BR>
     *
     * @return 上下文
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2023/3/16 10:20
     */
    @Override
    public IActionContext<OUT> getActionContext() {
        return context;
    }
}
