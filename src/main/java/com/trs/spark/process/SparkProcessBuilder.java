package com.trs.spark.process;

import com.trs.common.base.PreConditionCheck;
import com.trs.spark.action.BaseConvertAction;
import com.trs.spark.action.BaseFromAction;
import com.trs.spark.action.BasePeekAction;
import com.trs.spark.action.IAction;
import com.trs.spark.context.IActionContext;
import org.apache.spark.sql.SparkSession;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * spark的流程组装工具
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2023/3/16 10:02
 * @version 1.0
 * @since 1.0
 */
public class SparkProcessBuilder<R extends Serializable> {

    private Chain actionChain;

    private SparkProcessBuilder() {
        this.actionChain = new Chain();
    }

    public static <IN extends Serializable> SparkProcessBuilder<IN> newProcess() {
        SparkProcessBuilder<IN> builder = new SparkProcessBuilder<>();
        return builder;
    }

    public <IN extends Serializable, OUTPUT extends Serializable> SparkProcessBuilder<OUTPUT> from(@Nonnull BaseFromAction<IN, OUTPUT> action) {
        return addOneAction(action);
    }

    public <IN extends Serializable, OUTPUT extends Serializable> SparkProcessBuilder<OUTPUT> convert(@Nonnull BaseConvertAction<IN, OUTPUT> action) {
        return addOneAction(action);
    }

    public <IN extends Serializable, OUTPUT extends Serializable> SparkProcessBuilder<OUTPUT> filter(@Nonnull BaseConvertAction<IN, OUTPUT> action) {
        return addOneAction(action);
    }

    public <IN extends Serializable> SparkProcessBuilder<IN> peek(@Nonnull BasePeekAction<IN> action) {
        return addOneAction(action);
    }

    public <IN extends Serializable> SparkProcessBuilder<IN> save(@Nonnull BasePeekAction<IN> action) {
        return addOneAction(action);
    }

    public <IN extends Serializable> SparkProcessBuilder<IN> to(@Nonnull BasePeekAction<IN> action) {
        return addOneAction(action);
    }

    public <IN extends Serializable, OUTPUT extends Serializable> SparkProcessBuilder<OUTPUT> addOneAction(@Nonnull IAction<IN, OUTPUT> action) {
        PreConditionCheck.checkNotNull(action, "Action不能为空");
        actionChain.addOneAction(action);
        return (SparkProcessBuilder<OUTPUT>) this;
    }

    public Boolean hasNext() {
        return actionChain != null && actionChain.hasNext();
    }

    public IAction getNext() {
        if (hasNext()) {
            return actionChain.getNextAction();
        }
        return null;
    }

    public R doRun(SparkSession spark, Class<?> clazz) {
        return doRun(spark);
    }

    public R doRun(SparkSession spark) {
        IAction next;
        Serializable result = null;
        if (!hasNext()) {
            return null;
        }
        next = getNext();
        do {
            IAction current = next != null ? next : getNext();
            PreConditionCheck.checkNotNull(current);
            IActionContext tmp = current.getActionContext();
            Serializable in = Optional.ofNullable(tmp)
                    .map(IActionContext::prevAction)
                    .map(IAction::getExecutedResult)
                    .orElse(null);
            if (current.action(spark, in)) {
                result = current.getExecutedResult();
                if (tmp == null) {
                    break;
                }
                next = tmp.nextAction();
            }
        } while (next != null && hasNext());
        return (R) result;
    }

    private static class Chain {
        private IAction currentWrittenAction;

        private AtomicInteger writeIndex = new AtomicInteger(-1);

        private AtomicInteger readIndex = new AtomicInteger(0);

        private List<IAction> actionList = Collections.synchronizedList(new ArrayList<>());

        public Boolean hasNext() {
            return readIndex.get() >= 0 && readIndex.get() < actionList.size();
        }

        public IAction getNextAction() {
            PreConditionCheck.checkArgument(hasNext(), "已经没有下一个元素了！");
            synchronized (Chain.class) {
                return actionList.get(readIndex.getAndIncrement());
            }
        }


        public synchronized void addOneAction(IAction action) {
            PreConditionCheck.checkNotNull(action.getActionContext(), "初始Action错误，一个Action必须包含一个已实例化的ActionContext");
            IActionContext actionContext = action.getActionContext();
            if (currentWrittenAction == null) {
                this.currentWrittenAction = action;
                actionList.add(0, action);
                actionContext.setPrevAction(null);  //头结点是没有PrevAction的
                writeIndex.set(0);
                return;
            }
            //需要设置prevAction和nextAction
            IActionContext currentActionContext = currentWrittenAction.getActionContext();
            currentActionContext.setNextAction(action);
            actionContext.setPrevAction(currentWrittenAction);
            currentWrittenAction = action;
            //主执行链记录
            actionList.add(action);
            //当前index增加
            writeIndex.incrementAndGet();
        }
    }
}
